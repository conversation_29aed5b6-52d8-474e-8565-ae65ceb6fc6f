// 基础默认配置
const basicsConfig = {
  // 初始化地图默认基础配置
  map: {
    accessToken: 'pk.eyJ1Ijoibm9ydGh2aSIsImEiOiJjbGVydjM1OXYwMnpkM3BxZGw5Ynlrbm13In0.queCazXHMgl8WHfZ1lF4xg',
    center: [114.05528061331722, 22.54140197444606], //中心点
    zoom: 16, //默认缩放
    minZoom: 10, //最小缩放
    maxZoom: 20, //最大缩放
    projection: 'globe', //球形地图
    antialias: true, //抗锯齿, //球形地图
    style: {
      version: 8,
      sources: {
        'osm-tiles1': { type: 'raster', tiles: ['https://t4.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'], tileSize: 256, maxzoom: 18 },
        'osm-tiles2': { type: 'raster', tiles: ['https://t4.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'], tileSize: 256, maxzoom: 18 },
        //地形图
        Polyline_FT: { type: 'vector', scheme: 'tms', tiles: ['https://www.szwgft.cn:8889/geoserver/gwc/service/tms/1.0.0/Code%3APolyline_FT@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'] },
        address_source: { type: 'vector', tiles: ['https://www.szwgft.cn:8889/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ZONE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'], scheme: 'tms' }
      },
      glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
      layers: [
        { id: 'simple-tiles1', type: 'raster', source: 'osm-tiles1' },
        { id: 'simple-tiles2', type: 'raster', source: 'osm-tiles2' },
        {
          id: 'PolylineFT_line',
          type: 'line',
          minzoom: 16.5,
          'source-layer': 'Polyline_FT',
          source: 'Polyline_FT',
          paint: { 'line-width': 2, 'line-color': '#666' }
        },
        {
          id: 'zone-fill',
          type: 'fill',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          paint: {
            'fill-opacity': 0.4,
            'fill-outline-color': '#000000',
            'fill-color': [
              'case',
              ['==', ['get', 'Type'], '市政路'],
              '#999',
              ['==', ['get', 'Type'], '其他'],
              '#888',
              [
                'match',
                ['get', 'ManagerNam'], // type 等于 小区，根据 ManagerNam 设置颜色
                ['福中水务所'],
                '#ee0000',
                ['福东水务所'],
                '#bdfb9b',
                ['梅林水务所'],
                '#1677ff',
                ['香蜜水务所'],
                '#fa9600',
                '#999' // 默认颜色，如果 ManagerNam 不匹配以上任何值
              ]
            ]
          }
        },
        {
          id: 'zone-line2',
          type: 'line',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          filter: ['==', ['get', 'Zone_Name'], ''],
          paint: { 'line-width': 2, 'line-color': '#07c160' }
        },
        {
          id: 'zone-symbol',
          type: 'symbol',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          layout: { 'text-field': ['get', 'Zone_Name'], 'text-anchor': 'center', 'text-size': 10 },
          paint: { 'text-color': 'black', 'text-halo-color': 'white', 'text-halo-width': 1 }
        }
      ]
    }
  }
}

export default basicsConfig
