<template>
  <div class="zone-content">
    <Introduce :detail v-if="current === '简介'" />
    <FieldWork :code="detail.Zone_Code" v-if="current === '总阀总表'" />
    <Record :code="detail.Zone_Code" v-if="current === '日常维护'" />
    <SecondSource :code="detail.Zone_Code" v-if="current === '二供'" />
    <Material :code="detail.Client_Name" v-if="current === '档案'" />
    <div style="padding: 100rpx"></div>
  </div>
</template>

<script setup>
import { watch } from 'vue'
import Material from './Material.vue'
import FieldWork from './FieldWork.vue'
import Record from './Record.vue'
import SecondSource from './SecondSource.vue'
import Introduce from './Introduce.vue'
import { userHomeStore } from '/src/store/home.js'
const { deliveryDots } = userHomeStore()
const props = defineProps({ current: String, detail: Object })

watch(
  () => props.current,
  (newVal) => ['简介', '档案'].includes(newVal) && deliveryDots()
)
</script>
