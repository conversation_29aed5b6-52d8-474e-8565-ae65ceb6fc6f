<template>
  <div class="menu-container absolute">
    <!-- 装饰性背景元素 -->
    <div class="menu-decoration">
      <div class="decoration-circle decoration-circle-1"></div>
      <div class="decoration-circle decoration-circle-2"></div>
      <div class="decoration-circle decoration-circle-3"></div>
    </div>

    <!-- 毛玻璃背景 -->
    <div class="menu-backdrop"></div>

    <!-- 顶部拖拽指示器 -->
    <div class="drag-indicator"></div>

    <!-- 菜单内容 -->
    <div class="menu-content flex f-between">
      <template v-for="(item, index) in list" :key="item.id">
        <div class="menu-item f-xy-center" :class="getItemClass(item)" :style="{ animationDelay: `${index * 0.1}s` }" @click="handlerClick(item)">
          <!-- 背景光晕效果 -->
          <div class="item-glow" :class="getGlowClass(item)"></div>

          <div class="item-icon-wrapper">
            <!-- 图标容器 -->
            <div class="icon-container" :class="getIconContainerClass(item)">
              <image class="item-icon" :src="item.icon" mode="aspectFill" />
              <!-- 图标遮罩渐变 -->
              <div class="icon-overlay" :class="getIconOverlayClass(item)"></div>
            </div>

            <!-- 活跃状态指示器 -->
            <div class="active-indicator" v-if="item.type === 'field'">
              <div class="indicator-pulse"></div>
            </div>

            <!-- 功能徽章 -->
            <div class="feature-badge" v-if="item.type === 'gis'">
              <text class="badge-text">NEW</text>
            </div>
          </div>

          <text class="item-name">{{ item.name }}</text>

          <!-- 底部装饰线 -->
          <div class="item-underline" :class="getUnderlineClass(item)"></div>
        </div>
      </template>
    </div>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>
  </div>
</template>

<script setup>
const emit = defineEmits(['chane'])
const list = [
  {
    type: 'field',
    id: 1,
    name: '总阀总表',
    color: '#0fec22',
    path: '/src/pages/mine/children/field-work/map',
    icon: 'https://www.szwgft.cn:8090/AppIcon/d31d28fb-814a-479a-99a6-bf9424d8cf81.jpeg'
  },
  {
    type: 'gis',
    id: 2,
    color: '',
    name: '日常维护',
    path: '/src/pages/mine/children/record/map',
    icon: 'https://www.szwgft.cn:8090/AppIcon/51a83070-9187-4049-9bf7-a1c11f289561.jpeg'
  },
  {
    type: 'second',
    id: 3,
    color: '',
    name: '二供泵房',
    path: '/src/pages/mine/children/pump-house/pumpHouseMap',
    icon: 'https://www.szwgft.cn:8090/AppIcon/51a83070-9187-4049-9bf7-a1c11f289561.jpeg'
  },
  {
    type: 'file',
    id: 4,
    color: '',
    name: '小区档案',
    path: '/src/pages/mine/children/material/map',
    icon: 'https://www.szwgft.cn:8090/AppIcon/51a83070-9187-4049-9bf7-a1c11f289561.jpeg'
  }
]

function handlerClick(item) {
  emit('chane', item)
}

// 获取菜单项样式类
function getItemClass(item) {
  return `menu-item-${item.type}`
}

// 获取光晕效果类
function getGlowClass(item) {
  const glowMap = {
    field: 'glow-green',
    gis: 'glow-blue',
    second: 'glow-purple',
    file: 'glow-orange'
  }
  return glowMap[item.type] || 'glow-default'
}

// 获取图标容器类
function getIconContainerClass(item) {
  const containerMap = {
    field: 'icon-container-green',
    gis: 'icon-container-blue',
    second: 'icon-container-purple',
    file: 'icon-container-orange'
  }
  return containerMap[item.type] || 'icon-container-default'
}

// 获取图标遮罩类
function getIconOverlayClass(item) {
  const overlayMap = {
    field: 'overlay-green',
    gis: 'overlay-blue',
    second: 'overlay-purple',
    file: 'overlay-orange'
  }
  return overlayMap[item.type] || 'overlay-default'
}

// 获取底部装饰线类
function getUnderlineClass(item) {
  const underlineMap = {
    field: 'underline-green',
    gis: 'underline-blue',
    second: 'underline-purple',
    file: 'underline-orange'
  }
  return underlineMap[item.type] || 'underline-default'
}
</script>

<style lang="less" scoped>
.menu-container {
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  overflow: hidden;
}

/* 装饰性背景元素 */
.menu-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(116, 83, 199, 0.1) 0%, rgba(255, 107, 107, 0.1) 100%);
  animation: float 6s ease-in-out infinite;
}

.decoration-circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: -60rpx;
  left: 10%;
  animation-delay: 0s;
}

.decoration-circle-2 {
  width: 80rpx;
  height: 80rpx;
  top: -40rpx;
  right: 15%;
  animation-delay: 2s;
}

.decoration-circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: -50rpx;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
    opacity: 0.6;
  }
}

/* 毛玻璃背景 */
.menu-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.92) 0%, rgba(252, 252, 255, 0.88) 30%, rgba(248, 250, 252, 0.85) 100%);
  backdrop-filter: blur(24rpx) saturate(1.3);
  -webkit-backdrop-filter: blur(24rpx) saturate(1.3);
  border-radius: 28rpx 28rpx 0 0;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08), 0 -2rpx 16rpx rgba(0, 0, 0, 0.04), inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
  z-index: 2;
}

/* 拖拽指示器 */
.drag-indicator {
  position: absolute;
  top: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, rgba(200, 200, 200, 0.4) 0%, rgba(160, 160, 160, 0.7) 50%, rgba(200, 200, 200, 0.4) 100%);
  border-radius: 3rpx;
  z-index: 3;
}

.menu-content {
  position: relative;
  padding: 28rpx 24rpx 16rpx;
  z-index: 3;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
  padding: 12rpx 8rpx 8rpx;
  border-radius: 24rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  animation: slideUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(40rpx);

  &:active {
    transform: scale(0.95) translateY(-2rpx);
  }
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 背景光晕效果 */
.item-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  opacity: 0;
  transition: all 0.4s ease;
  z-index: -1;
}

.menu-item:active .item-glow {
  opacity: 0.6;
  transform: translate(-50%, -50%) scale(1.2);
}

.glow-green {
  background: radial-gradient(circle, rgba(34, 197, 94, 0.3) 0%, transparent 70%);
}
.glow-blue {
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
}
.glow-purple {
  background: radial-gradient(circle, rgba(147, 51, 234, 0.3) 0%, transparent 70%);
}
.glow-orange {
  background: radial-gradient(circle, rgba(249, 115, 22, 0.3) 0%, transparent 70%);
}

.item-icon-wrapper {
  position: relative;
  margin-bottom: 12rpx;
}

/* 图标容器 */
.icon-container {
  position: relative;
  width: 72rpx;
  height: 72rpx;
  border-radius: 22rpx;
  overflow: hidden;
  transition: all 0.4s ease;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12), 0 3rpx 8rpx rgba(0, 0, 0, 0.08);
}

.menu-item:active .icon-container {
  transform: scale(1.05);
  box-shadow: 0 12rpx 35rpx rgba(0, 0, 0, 0.2), 0 6rpx 15rpx rgba(0, 0, 0, 0.15);
}

.icon-container-green {
  border: 4rpx solid rgba(34, 197, 94, 0.3);
}
.icon-container-blue {
  border: 4rpx solid rgba(59, 130, 246, 0.3);
}
.icon-container-purple {
  border: 4rpx solid rgba(147, 51, 234, 0.3);
}
.icon-container-orange {
  border: 4rpx solid rgba(249, 115, 22, 0.3);
}

.item-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease;
}

.menu-item:active .item-icon {
  transform: scale(1.1);
}

/* 图标遮罩渐变 */
.icon-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: all 0.4s ease;
  mix-blend-mode: overlay;
}

.menu-item:active .icon-overlay {
  opacity: 0.3;
}

.overlay-green {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.6) 0%, rgba(22, 163, 74, 0.4) 100%);
}
.overlay-blue {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6) 0%, rgba(37, 99, 235, 0.4) 100%);
}
.overlay-purple {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.6) 0%, rgba(126, 34, 206, 0.4) 100%);
}
.overlay-orange {
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.6) 0%, rgba(234, 88, 12, 0.4) 100%);
}

/* 活跃状态指示器 */
.active-indicator {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 50%, #ff8e8e 100%);
  border: 4rpx solid #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4), 0 2rpx 6rpx rgba(255, 71, 87, 0.2);
  z-index: 10;
  overflow: hidden;
}

.indicator-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 12rpx;
  background: #ffffff;
  border-radius: 50%;
  animation: indicatorPulse 2s ease-in-out infinite;
}

@keyframes indicatorPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0.7;
  }
}

/* 功能徽章 */
.feature-badge {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-size: 18rpx;
  font-weight: 700;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
  z-index: 10;
  animation: badgeShine 3s ease-in-out infinite;
}

.badge-text {
  font-size: 18rpx;
  font-weight: 700;
  letter-spacing: 0.5rpx;
}

@keyframes badgeShine {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.6);
  }
}

/* 菜单项文字 */
.item-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #374151;
  text-align: center;
  line-height: 1.2;
  max-width: 100rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 4rpx;
}

.menu-item:active .item-name {
  color: #1a202c;
  transform: translateY(-2rpx);
}

/* 底部装饰线 */
.item-underline {
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 4rpx;
  border-radius: 2rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.menu-item:active .item-underline {
  width: 60rpx;
}

.underline-green {
  background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
}
.underline-blue {
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
}
.underline-purple {
  background: linear-gradient(90deg, #9333ea 0%, #7e22ce 100%);
}
.underline-orange {
  background: linear-gradient(90deg, #f97316 0%, #ea580c 100%);
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom, 24rpx);
  background: transparent;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .menu-backdrop {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.92) 0%, rgba(55, 65, 81, 0.88) 30%, rgba(75, 85, 99, 0.85) 100%);
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.25), 0 -2rpx 16rpx rgba(0, 0, 0, 0.15), inset 0 1rpx 0 rgba(255, 255, 255, 0.08);
  }

  .item-name {
    color: #e5e7eb;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.4);
  }

  .menu-item:active .item-name {
    color: #f9fafb;
  }

  .drag-indicator {
    background: linear-gradient(90deg, rgba(156, 163, 175, 0.4) 0%, rgba(209, 213, 219, 0.7) 50%, rgba(156, 163, 175, 0.4) 100%);
  }
}

/* 小屏幕适配 */
@media (max-width: 750rpx) {
  .menu-content {
    padding: 24rpx 20rpx 14rpx;
  }

  .menu-item {
    width: 110rpx;
    padding: 10rpx 6rpx 6rpx;
  }

  .icon-container {
    width: 68rpx;
    height: 68rpx;
    border-radius: 20rpx;
  }

  .item-name {
    font-size: 22rpx;
    max-width: 96rpx;
  }

  .active-indicator {
    width: 22rpx;
    height: 22rpx;
    top: -5rpx;
    right: -5rpx;
  }

  .indicator-pulse {
    width: 8rpx;
    height: 8rpx;
  }

  .decoration-circle-1 {
    width: 90rpx;
    height: 90rpx;
  }
  .decoration-circle-2 {
    width: 60rpx;
    height: 60rpx;
  }
  .decoration-circle-3 {
    width: 75rpx;
    height: 75rpx;
  }
}

/* 超小屏幕适配 */
@media (max-width: 600rpx) {
  .menu-content {
    padding: 20rpx 12rpx 12rpx;
  }

  .menu-item {
    width: 96rpx;
    padding: 8rpx 4rpx 4rpx;
  }

  .icon-container {
    width: 60rpx;
    height: 60rpx;
    border-radius: 18rpx;
  }

  .item-name {
    font-size: 20rpx;
    max-width: 84rpx;
  }

  .item-icon-wrapper {
    margin-bottom: 8rpx;
  }
}
</style>
