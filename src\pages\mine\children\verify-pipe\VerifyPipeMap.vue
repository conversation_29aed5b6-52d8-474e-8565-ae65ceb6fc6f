<template>
  <div class="all flex f-column">
    <wd-search v-model="seekText" @search="debounceHandlesearchChange" @change="debounceHandlesearchChange">
      <template #prefix>
        <wd-popover mode="menu" :content="menu" @menuclick="changeSearchType">
          <view class="search-type">
            <text>{{ searchType }}</text>
            <wd-icon custom-class="icon-arrow" name="fill-arrow-down"></wd-icon>
          </view>
        </wd-popover>
      </template>
    </wd-search>

    <div class="f-1"><MapGlS :variation @wdToast="handleWdToast" @openPopup="handleOpenPopup" @queryFeatures="handleQueryFeatures" @mapLoad="mapLoad" /></div>

    <div class="absolute box-shadow f-xy-center pad-10 back-white border-R12" @click="handleOpenStatistics" style="bottom: 280rpx; right: 50rpx">
      <wd-icon name="chart-pie" color="#2d2d2d" size="22px"></wd-icon>
    </div>
    <ControlLayer @change="layerChange" class="z-index-10" :layerList style="bottom: 190rpx; right: 50rpx" />
    <div class="fixed box-shadow f-xy-center pad-10 back-white border-R12" @click="handleOneself" style="bottom: 100rpx; right: 50rpx">
      <image style="width: 44rpx; height: 44rpx" src="/static/DW.png"></image>
    </div>
  </div>

  <FeedbackPopup ref="feedbackRef" :id="8" />

  <!-- 核查填写弹窗 -->
  <wd-popup v-model="linePopupOpen" :closable="false" position="bottom" custom-style="min-height: 1000rpx; z-index:99">
    <div class="verify-form-container">
      <!-- 表单标题和关闭按钮 -->
      <div class="form-header">
        <div class="form-title-bar">
          <div class="form-title">
            <wd-icon name="edit" size="20px" color="#1890ff"></wd-icon>
            <span>管道核查信息</span>
          </div>
          <div class="close-btn" @click="handleClosePopup">
            <wd-icon name="close" size="18px" color="#8c8c8c"></wd-icon>
          </div>
        </div>
      </div>

      <!-- 基础信息卡片 -->
      <div class="info-card">
        <div class="card-header">
          <wd-icon name="info" size="16px" color="#52c41a"></wd-icon>
          <span class="card-title">基础信息</span>
        </div>
        <div class="info-grid">
          <!-- 系统编码 - 独占一行 -->
          <div class="info-item primary full-width">
            <div class="info-label">
              <wd-icon name="barcode" size="14px" color="#1890ff"></wd-icon>
              系统编码
            </div>
            <div class="info-value">{{ lineDetail.SystemId }}</div>
          </div>

          <!-- 两列布局 -->
          <div class="info-row">
            <div class="info-item">
              <div class="info-label">
                <wd-icon name="setting" size="14px" color="#722ed1"></wd-icon>
                原始材质
              </div>
              <div class="info-value">{{ lineDetail.Material || '--' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">
                <wd-icon name="ruler" size="14px" color="#fa8c16"></wd-icon>
                管道管径
              </div>
              <div class="info-value">{{ lineDetail.DiaMeter || '--' }}</div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <div class="info-label">
                <wd-icon name="drop" size="14px" color="#13c2c2"></wd-icon>
                雨污类型
              </div>
              <div class="info-value">{{ lineDetail.SubType || '--' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">
                <wd-icon name="link" size="14px" color="#eb2f96"></wd-icon>
                管道类型
              </div>
              <div class="info-value">{{ lineDetail.TownType || '--' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 材质选择卡片 -->
      <div class="material-card">
        <div class="card-header">
          <wd-icon name="check-circle" size="16px" color="#52c41a"></wd-icon>
          <span class="card-title">选择实际材质</span>
          <span class="required-mark">*</span>
        </div>
        <div class="material-selection">
          <wd-radio-group v-model="lineDetail.NowMaterial" @change="({ value }) => (lineDetail.NowMaterial = value)" shape="dot" custom-class="material-radio-group">
            <template v-for="item in actionDrainagePipeMaterialActions" :key="item.name">
              <wd-radio :value="item.name" custom-class="material-radio-item">{{ item.name }}</wd-radio>
            </template>
          </wd-radio-group>
        </div>
      </div>

      <!-- 图片上传卡片 -->
      <div class="upload-card">
        <div class="card-header">
          <wd-icon name="camera" size="16px" color="#fa541c"></wd-icon>
          <span class="card-title">上传现场材质图片</span>
          <span class="required-mark">*</span>
        </div>
        <div class="upload-section">
          <div class="upload-tip">
            <wd-icon name="info-fill" size="14px" color="#1890ff"></wd-icon>
            <span>请上传清晰的现场材质照片，最多3张</span>
          </div>
          <UploadeImg v-model="lineDetail.SceneImg" :maxlength="3" :handleImg="({ data }) => data" url="https://www.szwgft.cn/nodeServer/resource/upload/verify_pipe" />
        </div>
      </div>
    </div>

    <!-- 提交按钮区域 -->
    <div class="submit-section">
      <wd-button @click="handleSubmitClick" custom-class="submit-btn" type="success" size="large">
        <wd-icon name="check" size="16px"></wd-icon>
        提交核查信息
      </wd-button>
    </div>
  </wd-popup>

  <!-- 搜索列表弹窗 -->
  <wd-popup :modal="false" v-model="searchOpen" closable position="bottom" custom-style="min-height: 1000rpx; z-index:99">
    <div class="pad-24">
      <div class="pad-Y32"></div>
      <div class="flex f-between" v-if="searchType == '编码'" style="background-color: #32cbdb">
        <div class="f-1 pad-16 text-center color-white">系统编码</div>
        <div class="f-1 pad-16 text-center color-white">原始材质</div>
        <div class="f-1 pad-16 text-center color-white">实际材质</div>
      </div>
      <div class="flex f-between" v-else style="background-color: #32cbdb">
        <div class="f-1 pad-16 text-center color-white">小区名称</div>
        <div class="f-1 pad-16 text-center color-white">小区编码</div>
      </div>
      <div class="overflow-auto" style="max-height: 800rpx">
        <template v-for="(item, index) in searchList" :key="index">
          <div class="item pad-16 mar-Y12 border-B-eee flex f-between" @click="searchItemClick(item)" v-if="searchType == '编码'">
            <div class="f-1 text-center" style="color: #333">{{ item.SystemId }}</div>
            <div class="f-1 text-center" style="color: dodgerblue">{{ item.Material }}</div>
            <div class="f-1 text-center">
              <div v-if="item.NowMaterial" style="color: #13a12f">{{ item.NowMaterial }}</div>
              <div v-else style="color: #ff6c37">未核查</div>
            </div>
          </div>
          <div class="item pad-16 mar-Y12 border-B-eee flex f-between" @click="searchItemClick(item)" v-else>
            <div class="f-1 text-center">{{ item.name }}</div>
            <div class="f-1 text-center">{{ item.Zone_Code }}</div>
          </div>
        </template>
        <wd-status-tip v-if="searchList.length == 0" image="search" tip="当前搜索无结果" />
      </div>
    </div>
  </wd-popup>

  <!-- 完成度统计 -->
  <wd-popup v-model="StatisticsOpen" closable position="center" custom-style="max-height: 80vh; width: 90%; border-radius: 16rpx; z-index:99">
    <div class="statistics-container">
      <!-- 统计标题 -->
      <div class="statistics-header">
        <div class="statistics-title">
          <wd-icon name="chart-pie" size="20px" color="#16baaa"></wd-icon>
          <span>完成度统计</span>
        </div>
      </div>

      <!-- 统计内容 -->
      <div class="statistics-content" v-if="StatisticsData.list">
        <!-- 总体统计 -->
        <div class="summary-card">
          <div class="summary-item">
            <div class="summary-label">核查总量</div>
            <wd-count-to :duration="1000" :endVal="StatisticsData.total" color="#16baaa" class="summary-value" />
          </div>
          <div class="summary-item">
            <div class="summary-label">已核查量</div>
            <wd-count-to :duration="1000" :endVal="StatisticsData.accomplish" color="#16baaa" class="summary-value" />
          </div>
          <div class="summary-item">
            <div class="summary-label">核查完成率</div>
            <wd-count-to :duration="1000" :decimals="2" :endVal="StatisticsData.schedule" suffix="%" color="#16baaa" class="summary-value" />
          </div>
        </div>

        <!-- 详细统计列表 -->
        <div class="detail-list">
          <template v-for="(item, index) in StatisticsData.list" :key="index">
            <div class="detail-item">
              <div class="detail-info">
                <div class="station-name">{{ item.Station }}</div>
                <div class="station-stats">
                  <span class="stat-text">总量：{{ item.total }}</span>
                  <span class="stat-text">核查：{{ item.accomplish }}</span>
                </div>
              </div>
              <div class="progress-circle">
                <wd-circle :modelValue="(item.accomplish / item.total) * 100" :size="50" :text="`${Math.round((item.accomplish / item.total) * 100, 2)}%`" :strokeWidth="12" color="#16baaa"></wd-circle>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </wd-popup>
  <wd-toast />
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { onShow, onNavigationBarButtonTap, onBackPress } from '@dcloudio/uni-app'

import MapGlS from './components/MapGlS/index.vue'

import { useToast } from 'wot-design-uni'
import debounce from 'lodash/debounce'

import ControlLayer from '/src/components/ControlLayer/index.vue'
import FeedbackPopup from '/src/components/FeedbackPopup/index.vue'

import { getZoneDataLike } from '/src/services/model/map.js'
import { CommonApi } from '/src/services/model/common'
import { VerifyPipeApi } from '/src/services/model/verify.js'
import UploadeImg from '/src/components/UploadeImg/index.vue'

const toast = useToast()

const variation = ref(null)
const linePopupOpen = ref(false)
const searchOpen = ref(false)
const StatisticsOpen = ref(false)
const drainagePipeMaterial = ref(null)
const actionDrainagePipeMaterialActions = ref([])

const feedbackRef = ref(null)

// 监听按钮点击事件
onNavigationBarButtonTap(() => feedbackRef.value.getFeedbackDetail())
onBackPress(() => {
  const isShow = feedbackRef.value.getFeedbackIsOpen()
  if (linePopupOpen.value === true || StatisticsOpen.value === true || searchOpen.value === true || isShow) {
    searchOpen.value = false
    linePopupOpen.value = false
    StatisticsOpen.value = false
    feedbackRef.value.close()
    return true
  }
})

onShow(async () => {
  try {
    if (!drainagePipeMaterial.value) {
      const { data } = await CommonApi.queryDictionaries('drainagePipeMaterial')
      actionDrainagePipeMaterialActions.value = data.map((i) => ({ name: i.DictValue, value: i.DictKey }))
    }
  } catch (error) {
    toast.error('获取数据失败')
  }
})

function mapLoad() {}

async function handleOneself() {
  toast.loading('正在获取位置')
  const { latitude, longitude } = await uni.getLocation({ altitude: true })
  variation.value = { type: 'moveto', value: [longitude, latitude] }
  toast.close()
}

const layerList = ref([{ layerId: 'line_a', checked: true, name: '需核对管网' }])

// 图层控制
function layerChange(e) {
  variation.value = { type: 'layerChange', value: e }
}

// 搜索功能
const seekText = ref('')
const searchType = ref('编码')
const menu = ref([{ content: '区块' }, { content: '编码' }])

function changeSearchType({ item }) {
  searchType.value = item.content
  searchOpen.value = false
  seekText.value = ''
}

function handleWdToast(value) {
  toast[value.Fn](value.text)
}

// 查询详情
const lineDetail = ref(null)
async function handleOpenPopup(value) {
  try {
    const { SYSTEMID } = value
    const { data } = await VerifyPipeApi.get(SYSTEMID)
    lineDetail.value = data
    linePopupOpen.value = true
  } catch (error) {
    toast.error('详情数据获取异常')
  }
}

// 查询已渲染要素完成数据
async function handleQueryFeatures(list) {
  try {
    if (!list.length) return
    const { data } = await VerifyPipeApi.distinguish({ list })
    const codes = data.filter((item) => item.NowMaterial && item.SceneImg).map((item) => item.SystemId)
    variation.value = { type: 'setPipeColor', value: codes }
  } catch (error) {
    toast.error('查询失败')
  }
}

// 关闭弹窗
function handleClosePopup() {
  linePopupOpen.value = false
}

// 提交数据
async function handleSubmitClick() {
  try {
    if (lineDetail.value.SceneImg && lineDetail.value.NowMaterial) {
      await VerifyPipeApi.update(lineDetail.value)
      variation.value = { type: 'renderMoveend', value: null }
      toast.success('提交成功')
      linePopupOpen.value = false
    } else {
      toast.warning('提交数据不完整')
    }
  } catch (error) {
    toast.error('提交失败')
  }
}

// 搜索
const searchList = ref([])
const debounceHandlesearchChange = debounce(searchChange, 500)
async function searchChange({ value }) {
  try {
    if (!value) return
    if (searchType.value === '区块') {
      const res = await getZoneDataLike(value)
      const result = JSON.parse(res.data)
      searchList.value = result.map((i) => ({ name: i.Zone_Name, Zone_Code: i.Zone_Code, Center_Point: i.Center_Point.split(',') }))
    } else {
      const { data } = await VerifyPipeApi.search({ systemId: value, pageSize: 20 })
      searchList.value = data.list
    }
    searchOpen.value = true
  } catch (error) {
    toast.error('搜索失败')
  }
}

// 搜索内容点击
function searchItemClick(item) {
  if (searchType.value === '区块') {
    variation.value = { type: 'reach', value: item }
    searchOpen.value = false
  } else {
    toast.loading('正在查询要素')
    variation.value = { type: 'searchPipe', value: item.SystemId }
    searchOpen.value = false
    handleOpenPopup({ SYSTEMID: item.SystemId })
    linePopupOpen.value = true
  }
}

// 统计数据
const StatisticsData = ref({})
async function handleOpenStatistics() {
  try {
    const { data } = await VerifyPipeApi.statistics()
    StatisticsData.value.list = data
    StatisticsData.value.total = data.reduce((a, b) => a + b.total, 0)
    StatisticsData.value.accomplish = data.reduce((a, b) => a + b.accomplish, 0)
    StatisticsData.value.schedule = (StatisticsData.value.accomplish / StatisticsData.value.total) * 100
    StatisticsOpen.value = true
  } catch (error) {
    toast.error('获取统计信息失败')
  }
}
</script>

<style lang="less" scoped>
:deep(.custom-button) {
  min-width: auto !important;
  box-sizing: border-box;
  width: 32px !important;
  height: 32px !important;
  border-radius: 16px !important;
  margin: 8rpx;
}

:deep(.custom-radio) {
  height: 32px !important;
  line-height: 32px !important;
}

:deep(.wd-checkbox) {
  margin-bottom: 6rpx;
}

:deep(.wd-checkbox__txt) {
  font-size: 20rpx;
  font-weight: 600;
}
:deep(.wd-popup) {
  background-color: #f6faff !important;
}
:deep(.wd-radio-group) {
  background-color: transparent;
}

/* 表单容器样式 */
.verify-form-container {
  padding: 24rpx;
  background: linear-gradient(135deg, #f6faff 0%, #e8f4fd 100%);
  min-height: 100%;
}

/* 表单标题 */
.form-header {
  margin-bottom: 32rpx;
  position: sticky;
  top: 0;
  z-index: 10;
  background: linear-gradient(135deg, #f6faff 0%, #e8f4fd 100%);
  padding-bottom: 16rpx;
}

.form-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8rpx;
}

.form-title {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
  padding: 16rpx 32rpx;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 50rpx;
  border: 2rpx solid rgba(24, 144, 255, 0.2);
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  background: rgba(140, 140, 140, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.close-btn:active {
  background: rgba(140, 140, 140, 0.2);
  transform: scale(0.95);
}

/* 卡片通用样式 */
.info-card,
.material-card,
.upload-card {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover,
.material-card:hover,
.upload-card:hover {
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
  transform: translateY(-2rpx);
}

/* 卡片标题 */
.card-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 24rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.02) 0%, rgba(255, 255, 255, 0) 100%);
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  flex: 1;
}

.required-mark {
  color: #ff4d4f;
  font-size: 24rpx;
  font-weight: 600;
}

/* 信息网格 */
.info-grid {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
  border-left: 4rpx solid #d9d9d9;
  transition: all 0.3s ease;
  min-height: 100rpx;
}

.info-item.primary {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(24, 144, 255, 0.02) 100%);
  border-left-color: #1890ff;
}

.info-item.full-width {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  min-height: auto;
}

.info-item:active {
  background: #f0f0f0;
  transform: scale(0.98);
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  color: #595959;
  font-weight: 500;
  line-height: 1.4;
}

.info-value {
  font-size: 24rpx;
  color: #262626;
  font-weight: 600;
  word-break: break-all;
  line-height: 1.4;
}

.full-width .info-value {
  text-align: right;
  max-width: 60%;
}

/* 材质选择样式 */
.material-selection {
  padding: 24rpx;
}

:deep(.material-radio-group) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 16rpx;
  background: transparent;
}

:deep(.material-radio-item) {
  background: #fafafa;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  margin: 0;
  transition: all 0.3s ease;
  text-align: center;
  font-size: 24rpx;
  font-weight: 500;
}

:deep(.material-radio-item.is-checked) {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  border-color: #1890ff;
  color: #1890ff;
  font-weight: 600;
  transform: scale(1.02);
}

:deep(.material-radio-item:hover) {
  border-color: #40a9ff;
  background: #f0f8ff;
}

/* 上传区域样式 */
.upload-section {
  padding: 24rpx;
}

.upload-tip {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(24, 144, 255, 0.02) 100%);
  border: 1rpx solid rgba(24, 144, 255, 0.2);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  font-size: 22rpx;
  color: #595959;
}

/* 提交按钮区域 */
.submit-section {
  padding: 32rpx 48rpx;
  background: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

:deep(.submit-btn) {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border: none;
  box-shadow: 0 4rpx 20rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

:deep(.submit-btn:active) {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(82, 196, 26, 0.4);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .info-grid {
    gap: 16rpx;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .info-item {
    padding: 16rpx;
    min-height: 80rpx;
  }

  .info-label {
    font-size: 20rpx;
  }

  .info-value {
    font-size: 22rpx;
  }

  :deep(.material-radio-group) {
    grid-template-columns: 1fr;
  }

  .form-title {
    font-size: 28rpx;
    padding: 12rpx 24rpx;
  }

  .close-btn {
    width: 56rpx;
    height: 56rpx;
  }

  .verify-form-container {
    padding: 20rpx;
  }
}

/* 统计弹窗样式 */
.statistics-container {
  padding: 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.statistics-header {
  margin-bottom: 24rpx;
  text-align: center;
}

.statistics-title {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #16baaa;
  padding: 16rpx 32rpx;
  background: rgba(22, 186, 170, 0.1);
  border-radius: 50rpx;
  border: 2rpx solid rgba(22, 186, 170, 0.2);
}

.statistics-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 总体统计卡片 */
.summary-card {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(22, 186, 170, 0.05) 0%, rgba(22, 186, 170, 0.02) 100%);
  border-radius: 16rpx;
  border: 1rpx solid rgba(22, 186, 170, 0.1);
}

.summary-item {
  text-align: center;
  padding: 16rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #595959;
  margin-bottom: 12rpx;
  font-weight: 500;
}

:deep(.summary-value) {
  font-size: 36rpx;
  font-weight: 600;
  color: #16baaa;
}

/* 详细统计列表 */
.detail-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 4rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background: #fafafa;
  border-radius: 12rpx;
  border-left: 4rpx solid #16baaa;
  transition: all 0.3s ease;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item:active {
  background: #f0f0f0;
  transform: scale(0.98);
}

.detail-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.station-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
}

.station-stats {
  display: flex;
  gap: 24rpx;
}

.stat-text {
  font-size: 22rpx;
  color: #595959;
}

.progress-circle {
  margin-left: 16rpx;
}

/* 响应式设计 - 统计弹窗 */
@media (max-width: 750rpx) {
  .summary-card {
    grid-template-columns: 1fr;
    gap: 12rpx;
    padding: 20rpx;
  }

  .summary-item {
    padding: 12rpx;
  }

  .summary-label {
    font-size: 22rpx;
  }

  :deep(.summary-value) {
    font-size: 32rpx;
  }

  .detail-item {
    padding: 16rpx;
  }

  .station-name {
    font-size: 26rpx;
  }

  .stat-text {
    font-size: 20rpx;
  }

  .station-stats {
    gap: 16rpx;
  }

  .statistics-title {
    font-size: 28rpx;
    padding: 12rpx 24rpx;
  }
}

.search-type {
  position: relative;
  height: 30px;
  line-height: 30px;
  padding: 0 8px 0 16px;
}

.search-type::after {
  position: absolute;
  content: '';
  width: 1px;
  right: 0;
  top: 5px;
  bottom: 5px;
  background: rgba(0, 0, 0, 0.25);
}

.search-type {
  :deep(.icon-arrow) {
    display: inline-block;
    font-size: 20px;
    vertical-align: middle;
  }
}
.item {
  background-color: #eee;
}
.item:nth-child(2n) {
  background-color: antiquewhite;
}
.item_ic {
  background-color: white;
  border-radius: 8rpx;
  width: 48%;
  height: 180rpx;
  margin: 12rpx 0;
  padding: 18rpx;
  box-sizing: border-box;
}
.item_s {
  border-right: 2rpx solid #eee;
  &:nth-child(3) {
    border: none;
  }
}
</style>
