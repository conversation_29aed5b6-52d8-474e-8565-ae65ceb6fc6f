/* flex */
.flex {
  display: flex;
}
.f-xy-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.f-x-center {
  display: flex;
  justify-content: center;
}
.f-y-center {
  display: flex;
  align-items: center;
}
.f-between {
  display: flex;
  justify-content: space-between;
}
.f-end {
  display: flex;
  justify-content: flex-end;
}
.f-wrap {
  flex-wrap: wrap;
}
.f-column {
  flex-direction: column;
}
.f-row-reverse {
  flex-direction: row-reverse;
}
.f-column-reverse {
  flex-direction: column-reverse;
}
.f-1 {
  flex: 1;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.fixed {
  position: fixed;
}
.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.absolute-all {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.z-index-9 {
  z-index: 9;
}
.z-index-10 {
  z-index: 10;
}
.z-index-99 {
  z-index: 99;
}
.z-index-100 {
  z-index: 100;
}
.z-index-999 {
  z-index: 999;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}
.overflow-scroll {
  overflow: scroll;
}

.box-shadow {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.middle {
  vertical-align: middle;
}

/* 单行文本 */
.text-nowrap {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.nowrap {
  white-space: nowrap;
}

/* 多行文本 */
.text-nowrap-2 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.text-nowrap-3 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.text-nowrap-4 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}
.text-nowrap-5 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
}
.text-nowrap-6 {
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
}

.border-eee {
  border: 2rpx solid #eee;
}
.border-B-eee {
  border-bottom: 2rpx solid #eee;
}

/* grid */
.grid {
  display: grid;
}
.grid-col2 {
  grid-template-columns: 1fr 1fr;
}
.grid-col3 {
  grid-template-columns: 1fr 1fr 1fr;
}
.grid-col4 {
  grid-template-columns: 1fr 1fr 1fr 1fr;
}
.grid-col5 {
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
}
.grid-row2 {
  grid-template-rows: 1fr 1fr;
}
.grid-row3 {
  grid-template-rows: 1fr 1fr 1fr;
}
.grid-row4 {
  grid-template-rows: 1fr 1fr 1fr 1fr;
}
.grid-row5 {
  grid-template-rows: 1fr 1fr 1fr 1fr;
}

/*  */

.all {
  width: 100%;
  height: 100%;
}
.W-auot {
  width: auto;
}
.H-auto {
  height: auto;
}
.W100 {
  width: 100%;
}
.H100 {
  height: 100%;
}

.H100vh {
  height: 100vh;
}
.H100vw {
  width: 100vw;
}
.H100vh60 {
  height: calc(100vh - 60rpx);
}
.H100vh50 {
  height: calc(100vh - 50rpx);
}
.WH160 {
  width: 160rpx;
  height: 160rpx;
}
.WH200 {
  width: 200rpx;
  height: 200rpx;
}
.WH240 {
  width: 240rpx;
  height: 240rpx;
}

/* --------------------------------------字体----------------------------------------*/
.fon-S20 {
  font-size: 20rpx;
}
.fon-S22 {
  font-size: 22rpx;
}
.fon-S24 {
  font-size: 24rpx;
}
.fon-S26 {
  font-size: 26rpx;
}
.fon-S28 {
  font-size: 28rpx;
}
.fon-S30 {
  font-size: 30rpx;
}
.fon-S32 {
  font-size: 32rpx;
}
.fon-S34 {
  font-size: 34rpx;
}
.fon-S36 {
  font-size: 36rpx;
}
.fon-S38 {
  font-size: 38rpx;
}
.fon-S40 {
  font-size: 40rpx;
}
.fon-S42 {
  font-size: 42rpx;
}
.fon-S44 {
  font-size: 44rpx;
}
.fon-S46 {
  font-size: 46rpx;
}
.fon-S48 {
  font-size: 48rpx;
}
.fon-S50 {
  font-size: 50rpx;
}
.fon-S52 {
  font-size: 52rpx;
}

.fon-W500 {
  font-weight: 500;
}
.fon-W600 {
  font-weight: 600;
}
.fon-W700 {
  font-weight: 700;
}

.back-transparent {
  background-color: transparent;
}
.back-white {
  background-color: #fff;
}
.back-f5f5f5 {
  background-color: #f5f5f5;
}
.back-eee {
  background-color: #eee;
}
.back-f0f0f0 {
  background-color: #f0f0f0;
}
.back-ccc {
  background-color: #ccc;
}
.back-999 {
  background-color: #999;
}
.back-666 {
  background-color: #666;
}
.back-333 {
  background-color: #333;
}
.back-000 {
  background-color: #000;
}
.back-pink {
  background-color: pink;
}

.pointer {
  cursor: pointer;
}

.color-white {
  color: #fff;
}
.color-333 {
  color: #333;
}
.color-666 {
  color: #666;
}
.color-999 {
  color: #999;
}
.color-ccc {
  color: #ccc;
}

.border-R4 {
  border-radius: 4rpx;
}
.border-R6 {
  border-radius: 6rpx;
}
.border-R8 {
  border-radius: 8rpx;
}
.border-R10 {
  border-radius: 10rpx;
}
.border-R12 {
  border-radius: 12rpx;
}
.border-R14 {
  border-radius: 14rpx;
}
.border-R16 {
  border-radius: 16rpx;
}
.border-R18 {
  border-radius: 18rpx;
}
.border-R20 {
  border-radius: 20rpx;
}
.border-R22 {
  border-radius: 22rpx;
}
.border-R24 {
  border-radius: 24rpx;
}
.border-R26 {
  border-radius: 26rpx;
}
.border-Rall {
  border-radius: 50%;
}

.pad-4 {
  padding: 4rpx;
}
.pad-6 {
  padding: 6rpx;
}
.pad-8 {
  padding: 8rpx;
}
.pad-10 {
  padding: 10rpx;
}
.pad-12 {
  padding: 12rpx;
}
.pad-14 {
  padding: 14rpx;
}
.pad-16 {
  padding: 16rpx;
}
.pad-18 {
  padding: 18rpx;
}
.pad-20 {
  padding: 20rpx;
}
.pad-22 {
  padding: 22rpx;
}
.pad-24 {
  padding: 24rpx;
}
.pad-26 {
  padding: 26rpx;
}
.pad-28 {
  padding: 28rpx;
}
.pad-30 {
  padding: 30rpx;
}
.pad-32 {
  padding: 32rpx;
}
.pad-34 {
  padding: 34rpx;
}
.pad-36 {
  padding: 36rpx;
}

.pad-L6 {
  padding-left: 6rpx;
}
.pad-L8 {
  padding-left: 8rpx;
}
.pad-L10 {
  padding-left: 10rpx;
}
.pad-L12 {
  padding-left: 12rpx;
}
.pad-L14 {
  padding-left: 14rpx;
}
.pad-L16 {
  padding-left: 16rpx;
}
.pad-L18 {
  padding-left: 18rpx;
}
.pad-L20 {
  padding-left: 20rpx;
}
.pad-L22 {
  padding-left: 22rpx;
}
.pad-L24 {
  padding-left: 24rpx;
}
.pad-L26 {
  padding-left: 26rpx;
}
.pad-L28 {
  padding-left: 28rpx;
}
.pad-L30 {
  padding-left: 30rpx;
}
.pad-L32 {
  padding-left: 32rpx;
}
.pad-L34 {
  padding-left: 34rpx;
}
.pad-L36 {
  padding-left: 36rpx;
}
.pad-R6 {
  padding-right: 6rpx;
}
.pad-R8 {
  padding-right: 8rpx;
}
.pad-R10 {
  padding-right: 10rpx;
}
.pad-R12 {
  padding-right: 12rpx;
}
.pad-R14 {
  padding-right: 14rpx;
}
.pad-R16 {
  padding-right: 16rpx;
}
.pad-R18 {
  padding-right: 18rpx;
}
.pad-R20 {
  padding-right: 20rpx;
}
.pad-R22 {
  padding-right: 22rpx;
}
.pad-R24 {
  padding-right: 24rpx;
}
.pad-R26 {
  padding-right: 26rpx;
}
.pad-R28 {
  padding-right: 28rpx;
}
.pad-R30 {
  padding-right: 30rpx;
}
.pad-R32 {
  padding-right: 32rpx;
}
.pad-R34 {
  padding-right: 34rpx;
}
.pad-R36 {
  padding-right: 36rpx;
}
.pad-T4 {
  padding-top: 4rpx;
}
.pad-T6 {
  padding-top: 6rpx;
}
.pad-T8 {
  padding-top: 8rpx;
}
.pad-T10 {
  padding-top: 10;
}
.pad-T12 {
  padding-top: 12rpx;
}
.pad-T14 {
  padding-top: 14rpx;
}
.pad-T16 {
  padding-top: 16rpx;
}
.pad-T18 {
  padding-top: 18rpx;
}
.pad-T20 {
  padding-top: 20rpx;
}
.pad-T22 {
  padding-top: 22rpx;
}
.pad-T24 {
  padding-top: 24rpx;
}
.pad-T26 {
  padding-top: 26rpx;
}
.pad-T28 {
  padding-top: 28rpx;
}
.pad-T30 {
  padding-top: 30rpx;
}
.pad-T32 {
  padding-top: 32rpx;
}
.pad-T34 {
  padding-top: 34rpx;
}
.pad-T36 {
  padding-top: 36rpx;
}

.pad-B4 {
  padding-bottom: 4rpx;
}
.pad-B6 {
  padding-bottom: 6rpx;
}
.pad-B8 {
  padding-bottom: 8rpx;
}
.pad-B10 {
  padding-bottom: 10;
}
.pad-B12 {
  padding-bottom: 12rpx;
}
.pad-B14 {
  padding-bottom: 14rpx;
}
.pad-B16 {
  padding-bottom: 16rpx;
}
.pad-B18 {
  padding-bottom: 18rpx;
}
.pad-B20 {
  padding-bottom: 20rpx;
}
.pad-B22 {
  padding-bottom: 22rpx;
}
.pad-B24 {
  padding-bottom: 24rpx;
}
.pad-B26 {
  padding-bottom: 26rpx;
}
.pad-B28 {
  padding-bottom: 28rpx;
}
.pad-B30 {
  padding-bottom: 30rpx;
}
.pad-B32 {
  padding-bottom: 32rpx;
}
.pad-B34 {
  padding-bottom: 34rpx;
}
.pad-B36 {
  padding-bottom: 36rpx;
}

.pad-Y4 {
  padding: 4rpx 0;
}
.pad-Y6 {
  padding: 6rpx 0;
}
.pad-Y8 {
  padding: 8rpx 0;
}
.pad-Y10 {
  padding: 10rpx 0;
}
.pad-Y12 {
  padding: 12rpx 0;
}
.pad-Y14 {
  padding: 14rpx 0;
}
.pad-Y16 {
  padding: 16rpx 0;
}
.pad-Y18 {
  padding: 18rpx;
}
.pad-Y20 {
  padding: 20rpx 0;
}
.pad-Y22 {
  padding: 22rpx 0;
}
.pad-Y24 {
  padding: 24rpx 0;
}
.pad-Y26 {
  padding: 26rpx 0;
}
.pad-Y28 {
  padding: 28rpx 0;
}
.pad-Y30 {
  padding: 30rpx 0;
}
.pad-Y32 {
  padding: 32rpx 0;
}
.pad-Y34 {
  padding: 34rpx 0;
}
.pad-Y36 {
  padding: 36rpx 0;
}

.pad-X4 {
  padding: 0 4rpx;
}
.pad-X6 {
  padding: 0 6rpx;
}
.pad-X8 {
  padding: 0 8rpx;
}
.pad-X10 {
  padding: 0 10rpx;
}
.pad-X12 {
  padding: 0 12rpx;
}
.pad-X14 {
  padding: 0 14rpx;
}
.pad-X16 {
  padding: 0 16rpx;
}
.pad-X18 {
  padding: 0 18rpx;
}
.pad-X20 {
  padding: 0 20rpx;
}
.pad-X22 {
  padding: 0 22rpx;
}
.pad-X24 {
  padding: 0 24rpx;
}
.pad-X26 {
  padding: 0 26rpx;
}
.pad-X28 {
  padding: 0 28rpx;
}
.pad-X30 {
  padding: 0 30rpx;
}
.pad-X32 {
  padding: 0 32rpx;
}
.pad-X34 {
  padding: 0 34rpx;
}
.pad-X36 {
  padding: 0 36rpx;
}
.pad-X38 {
  padding: 0 38rpx;
}
.pad-X40 {
  padding: 0 40rpx;
}
.pad-X42 {
  padding: 0 42rpx;
}

.pad-X48 {
  padding: 0 48rpx;
}
.mar-4 {
  margin: 4rpx;
}
.mar-6 {
  margin: 6rpx;
}
.mar-8 {
  margin: 8rpx;
}
.mar-10 {
  margin: 10rpx;
}
.mar-12 {
  margin: 12rpx;
}
.mar-14 {
  margin: 14rpx;
}
.mar-16 {
  margin: 16rpx;
}
.mar-18 {
  margin: 18rpx;
}
.mar-20 {
  margin: 20rpx;
}
.mar-22 {
  margin: 22rpx;
}
.mar-24 {
  margin: 24rpx;
}
.mar-26 {
  margin: 26rpx;
}
.mar-28 {
  margin: 28rpx;
}
.mar-30 {
  margin: 30rpx;
}
.mar-T4 {
  margin-top: 4rpx;
}
.mar-T6 {
  margin-top: 6rpx;
}
.mar-T8 {
  margin-top: 8rpx;
}
.mar-T10 {
  margin-top: 10rpx;
}
.mar-T12 {
  margin-top: 12rpx;
}
.mar-T14 {
  margin-top: 14rpx;
}
.mar-T16 {
  margin-top: 16rpx;
}
.mar-T18 {
  margin-top: 18rpx;
}
.mar-T20 {
  margin-top: 20rpx;
}
.mar-T22 {
  margin-top: 22rpx;
}
.mar-T24 {
  margin-top: 24rpx;
}
.mar-T26 {
  margin-top: 26rpx;
}
.mar-T28 {
  margin-top: 28rpx;
}
.mar-T30 {
  margin-top: 30rpx;
}
.mar-B4 {
  margin-bottom: 4rpx;
}
.mar-B6 {
  margin-bottom: 6rpx;
}
.mar-B8 {
  margin-bottom: 8rpx;
}
.mar-B10 {
  margin-bottom: 10rpx;
}
.mar-B12 {
  margin-bottom: 12rpx;
}
.mar-B14 {
  margin-bottom: 14rpx;
}
.mar-B16 {
  margin-bottom: 16rpx;
}
.mar-B18 {
  margin-bottom: 18rpx;
}
.mar-B20 {
  margin-bottom: 20rpx;
}
.mar-B22 {
  margin-bottom: 22rpx;
}
.mar-B24 {
  margin-bottom: 24rpx;
}
.mar-B26 {
  margin-bottom: 26rpx;
}
.mar-B28 {
  margin-bottom: 28rpx;
}
.mar-B30 {
  margin-bottom: 30rpx;
}
.mar-L4 {
  margin-left: 4rpx;
}
.mar-L6 {
  margin-left: 6rpx;
}
.mar-L8 {
  margin-left: 8rpx;
}
.mar-L10 {
  margin-left: 10rpx;
}
.mar-L12 {
  margin-left: 12rpx;
}
.mar-L14 {
  margin-left: 14rpx;
}
.mar-L16 {
  margin-left: 16rpx;
}
.mar-L18 {
  margin-left: 18rpx;
}
.mar-L20 {
  margin-left: 20rpx;
}
.mar-L22 {
  margin-left: 22rpx;
}
.mar-L24 {
  margin-left: 24rpx;
}
.mar-L26 {
  margin-left: 26rpx;
}
.mar-L28 {
  margin-left: 28rpx;
}
.mar-L30 {
  margin-left: 30rpx;
}
.mar-R4 {
  margin-right: 4rpx;
}
.mar-R6 {
  margin-right: 6rpx;
}
.mar-R8 {
  margin-right: 8rpx;
}
.mar-R10 {
  margin-right: 10rpx;
}
.mar-R12 {
  margin-right: 12rpx;
}
.mar-R14 {
  margin-right: 14rpx;
}
.mar-R16 {
  margin-right: 16rpx;
}
.mar-R18 {
  margin-right: 18rpx;
}
.mar-R20 {
  margin-right: 20rpx;
}
.mar-R22 {
  margin-right: 22rpx;
}
.mar-R24 {
  margin-right: 24rpx;
}
.mar-R26 {
  margin-right: 26rpx;
}
.mar-R28 {
  margin-right: 28rpx;
}
.mar-R30 {
  margin-right: 30rpx;
}
.mar-X4 {
  margin: 0 4rpx;
}
.mar-X6 {
  margin: 0 6rpx;
}
.mar-X8 {
  margin: 0 8rpx;
}
.mar-X10 {
  margin: 0 10rpx;
}
.mar-X12 {
  margin: 0 12rpx;
}
.mar-X14 {
  margin: 0 14rpx;
}
.mar-X16 {
  margin: 0 16rpx;
}
.mar-X18 {
  margin: 0 18rpx;
}
.mar-X20 {
  margin: 0 20rpx;
}
.mar-X22 {
  margin: 0 22rpx;
}
.mar-X24 {
  margin: 0 24rpx;
}
.mar-X26 {
  margin: 0 26rpx;
}
.mar-X28 {
  margin: 0 28rpx;
}
.mar-X30 {
  margin: 0 30rpx;
}
.mar-Y4 {
  margin: 4rpx 0;
}
.mar-Y6 {
  margin: 6rpx 0;
}
.mar-Y8 {
  margin: 8rpx 0;
}
.mar-Y10 {
  margin: 10rpx 0;
}
.mar-Y12 {
  margin: 12rpx 0;
}
.mar-Y14 {
  margin: 14rpx 0;
}
.mar-Y16 {
  margin: 16rpx 0;
}
.mar-Y18 {
  margin: 18rpx 0;
}
.mar-Y20 {
  margin: 20rpx 0;
}
.mar-Y22 {
  margin: 22rpx 0;
}
.mar-Y24 {
  margin: 24rpx 0;
}
.mar-Y26 {
  margin: 26rpx 0;
}
.mar-Y28 {
  margin: 28rpx 0;
}
.mar-Y30 {
  margin: 30rpx 0;
}
/* 无子元素不展示 */
.empty:empty {
  display: none;
}
