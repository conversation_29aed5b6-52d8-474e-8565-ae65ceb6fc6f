<template>
  <div>
    <template v-for="item in list" :key="item.id">
      <wd-card @click="handleClick(item)" class="box-shadow">
        <div class="f-y-center">
          <text class="text-nowrap-3">{{ item.showRemark }}</text>
        </div>
        <template #title>
          <div class="f-between">
            <div class="f-1 text-nowrap">{{ item.PumpHouseName }}</div>
            <div class="mar-L12">
              <wd-tag custom-class="space" v-if="item.progressStatus === '停工'" type="danger">{{ item.ProgressStatus }}</wd-tag>
              <wd-tag custom-class="space" v-if="item.progressStatus === '滞后'" type="warning">{{ item.ProgressStatus }}</wd-tag>
              <wd-tag custom-class="space" v-if="item.progressStatus === '正常'" type="success">{{ item.ProgressStatus }}</wd-tag>
            </div>
          </div>
        </template>
        <template #footer>
          <div class="f-between">
            <div>{{ item.BelongingArea }}</div>
            <div>{{ item.UpdateTime && item.UpdateTime.slice(0, 10) }}</div>
          </div>
        </template>
      </wd-card>
    </template>
  </div>
  <div class="back-white border-R14 box-shadow" v-if="!list.length">
    <wd-status-tip image="search" tip="无数据" />
  </div>
  <div style="padding: 80rpx"></div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { handlerImage } from '/src/utils/index.js'
import { getZoneCodeLikeApi } from '/src/services/model/secondSorce.js'
import { userHomeStore } from '/src/store/home.js'
import uniUtil from '/src/utils/uniUtil.js'
import { PumpHouseApi } from '/src/services/model/pump.house.js'

const { deliveryDots } = userHomeStore()

const props = defineProps({ code: String })

watch(() => props.code, getList, { immediate: true })

const list = ref([])
watch(list, (val) => deliveryDots(val, 'second'))
async function getList(zoneCode) {
  try {
    // const result = await getZoneCodeLikeApi(zoneCode)
    // list.value = result.data.secondaryWaterProgress
    const { data } = await PumpHouseApi.seekScope(zoneCode)
    list.value = data
  } catch (error) {
    list.value = []
  }
}

function handleClick(val) {
  uniUtil.navigateTo(`/src/pages/mine/children/pump-house/pumpHouseMap?PumpRoomNumber=${val.PumpRoomNumber}`)
}
</script>
