<template>
  <view class="popup-menu pad-X12 z-index-999 border-R12 f-column">
    <movable-view class="W100" @touchstart.stop="handleTouchStart" @touchmove.stop="handleTouchMove" @touchend.stop="handleTouchEnd">
      <view class="all mar-T24 pad-X12 border-R12">
        <view class="empty box-shadow mar-B12"> <slot name="top"></slot></view>
        <view class="empty box-shadow back-white mar-B12 border-R12 border-B-eee pad-24"><slot name="menu"></slot></view>
        <view class="empty box-shadow back-white mar-B12 border-R12 border-B-eee pad-24"><slot name="interlayer"></slot></view>
        <scroll-view :scroll-y="true" class="empty f-1 content" @scroll="contentScroll" @touchmove="handleContentTouchMove" @touchend="handleContentTouchEnd" @touchstart="handleContentTouchStart" :style="{ height }">
          <slot name="content"> </slot>
        </scroll-view>
      </view>
    </movable-view>
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import throttle from 'lodash/throttle'
import { useCommonStore } from '/src/store/common.js'
import { storeToRefs } from 'pinia'
const emits = defineEmits(['horizontalSlip'])

const popupMenuHeight = ref('50vh')
const startY = ref(0)
const currentY = ref(0)
const startX = ref(0)
const transition = ref('none')
const { screenInformation } = storeToRefs(useCommonStore())
const { statusBarHeight } = screenInformation.value

const height = computed(() => (state.value === 0 ? '50vh' : state.value === 1 ? '100vh' : '0px'))
//状态

const state = defineModel({ default: 0 })

// 开始位置
function handleTouchStart(event) {
  // 记录触摸开始时指针的垂直位置
  startY.value = event.touches[0].clientY
  currentY.value = 0 //置空

  startX.value = event.touches[0].clientX
}
// 滑动事件
const throttledHandleTouchMove = throttle(
  (event) => {
    // 更新当前Y轴坐标，取第一个触摸点的位置
    currentY.value = event.touches[0].clientY
    // 计算Y轴上的移动距离，起始位置减去当前位置
    const deltaY = startY.value - currentY.value
    // 根据移动距离动态计算弹出菜单的新高度
    const newHeight = `calc(${height.value} + ${deltaY}px)`
    // // 更新弹出菜单高度的绑定值
    if (state.value == 1 && deltaY > 0) return // 限制最大高度
    transition.value = 'none'
    popupMenuHeight.value = newHeight
  },
  16,
  { trailing: false }
) // 约60fps的频率

function handleTouchMove(event) {
  throttledHandleTouchMove(event)
}

// 滑动结束事件
function handleTouchEnd(event) {
  // 左右滑动
  const offsetX = startX.value - event.changedTouches[0].clientX
  if (offsetX > 0) {
    // 左滑
    if (offsetX > 80) emits('horizontalSlip', true)
  } else {
    // 右滑
    if (offsetX < -80) emits('horizontalSlip', false)
  }

  // 上下滑动动
  if (currentY.value === 0) return
  const offsetY = currentY.value - startY.value
  if (offsetY < 0) {
    // 向上滑动
    if (offsetY < -80 && state.value === 0) {
      state.value = 1
    } else if (state.value === -1) {
      state.value = 0
    }
  } else {
    // 向下滑动
    if (offsetY > 200) {
      state.value = -1
    } else if (offsetY > 80 && state.value === 1) {
      state.value = 0
    } else if (offsetY > 80 && state.value === 0) {
      state.value = -1
    }
  }
  changeHeight()
}

watch(state, () => changeHeight(), { immediate: true })
// 改变上拉框的高度
function changeHeight() {
  transition.value = 'height 0.6s ease'
  popupMenuHeight.value = height.value
}

function handleContentTouchStart(event) {
  if (shouldStopPropagation(event)) {
    event.stopPropagation()
  }
}

function handleContentTouchMove(event) {
  if (shouldStopPropagation(event)) {
    event.stopPropagation()
  }
}

function handleContentTouchEnd(event) {
  if (shouldStopPropagation(event)) {
    event.stopPropagation()
  }
}

function shouldStopPropagation(event) {
  return true
}

function contentScroll(event) {
  if (height.value !== '100vh') state.value = 1
}
</script>

<style lang="less" scoped>
.popup-menu {
  height: v-bind(popupMenuHeight);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  transition: v-bind(transition); // 添加过渡效果
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.6);
  &::before {
    content: '';
    width: 60rpx;
    height: 8rpx;
    background-color: #999;
    position: absolute;
    top: 10rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 7rpx;
  }
}
.content {
  transition: height 0.6s ease; // 添加过渡效果
}
</style>
