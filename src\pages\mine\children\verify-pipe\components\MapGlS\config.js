// 基础默认配置
const basicsConfig = {
  // 初始化地图默认基础配置
  map: {
    accessToken: 'pk.eyJ1Ijoibm9ydGh2aSIsImEiOiJjbGVydjM1OXYwMnpkM3BxZGw5Ynlrbm13In0.queCazXHMgl8WHfZ1lF4xg',
    center: [114.05528061331722, 22.54140197444606, 0], //中心点
    zoom: 12, //默认缩放
    minZoom: 10, //最小缩放
    maxZoom: 22, //最大缩放
    projection: 'globe', //球形地图
    antialias: true, //抗锯齿, //球形地图
    style: {
      version: 8,
      sources: {
        // 天地图矢量
        'osm-tiles1': {
          type: 'raster',
          tiles: ['https://t4.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'],
          tileSize: 256,
          maxzoom: 18
        },
        'osm-tiles2': {
          type: 'raster',
          tiles: ['https://t4.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'],
          tileSize: 256,
          maxzoom: 18
        },
        //地形图
        Polyline_FT: {
          type: 'vector',
          scheme: 'tms',
          tiles: ['https://www.szwgft.cn:8889/geoserver/gwc/service/tms/1.0.0/Code%3APolyline_FT@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf']
        },
        // 无缝区块
        address_source: {
          type: 'vector',
          scheme: 'tms',
          tiles: ['https://www.szwgft.cn:8889/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ZONE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf']
        },
        // 需核查图层
        tubeLine: {
          type: 'vector',
          scheme: 'tms',
          tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AverifyPipe@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf']
        }
      },
      glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
      layers: [
        // 天地图底图
        {
          id: 'simple-tiles1',
          type: 'raster',
          source: 'osm-tiles1'
        },
        {
          id: 'simple-tiles2',
          type: 'raster',
          source: 'osm-tiles2'
        },
        {
          id: 'PolylineFT_line',
          type: 'line',
          minzoom: 16.5,
          'source-layer': 'Polyline_FT',
          source: 'Polyline_FT',
          paint: { 'line-width': 2, 'line-color': '#666' }
        },

        // 无缝区块
        {
          id: 'zone-fill',
          type: 'fill',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          paint: {
            'fill-opacity': 0.35,
            'fill-color': [
              'case',
              ['==', ['get', 'Type'], '市政路'],
              '#999',
              ['==', ['get', 'Type'], '其他'],
              '#888',
              [
                'match',
                ['get', 'ManagerNam'], // type 等于 小区，根据 ManagerNam 设置颜色
                ['福中水务所'],
                '#ee0000',
                ['福东水务所'],
                '#bdfb9b',
                ['梅林水务所'],
                '#1677ff',
                ['香蜜水务所'],
                '#fa9600',
                '#999' // 默认颜色，如果 ManagerNam 不匹配以上任何值
              ]
            ]
          }
        },
        {
          id: 'zone-line',
          type: 'line',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          paint: {
            'line-width': 1,
            'line-color': '#343434'
          }
        },
        {
          id: 'zone-line2',
          type: 'line',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          filter: ['==', ['get', 'Zone_Name'], ''],
          paint: {
            'line-width': 5,
            'line-color': '#07c160'
          }
        },
        {
          id: 'zone-symbol',
          type: 'symbol',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          layout: {
            'text-field': ['get', 'Zone_Name'],
            'text-anchor': 'center',
            'text-size': 10
          },
          paint: {
            'text-color': 'black',
            'text-halo-color': 'white',
            'text-halo-width': 1
          }
        },
        // 需核查图层
        {
          id: 'line_a',
          type: 'line',
          source: 'tubeLine',
          'source-layer': 'verifyPipe',
          layout: { 'line-join': 'round', 'line-cap': 'round' },
          paint: {
            'line-color': '#666',
            'line-width': 2,
            'line-opacity': 1
          }
        },
        {
          id: 'line_active',
          type: 'line',
          source: 'tubeLine',
          'source-layer': 'verifyPipe',
          layout: { 'line-join': 'round', 'line-cap': 'round' },
          paint: {
            'line-color': '#00dcff',
            'line-width': 10,
            'line-opacity': 0
          }
        },
        {
          id: 'line_l',
          type: 'line',
          source: 'tubeLine',
          'source-layer': 'verifyPipe',
          minzoom: 15.5,
          layout: { 'line-join': 'round', 'line-cap': 'round' },
          paint: {
            'line-color': '#f5bd00',
            'line-width': 6,
            'line-opacity': 1
          }
        },
        {
          id: 'line_symbol',
          type: 'symbol',
          source: 'tubeLine',
          'source-layer': 'verifyPipe',
          minzoom: 16,
          paint: { 'text-color': '#0076b8' },
          layout: {
            'text-field': ['get', 'MATERIAL'],
            'symbol-placement': 'line-center',
            'text-rotation-alignment': 'map',
            'text-offset': [0, 1],
            'text-size': 12,
            'text-anchor': 'center'
          }
        }
      ]
    }
  }
}

export default basicsConfig
