# FloatingButton 悬浮按钮组件

一个功能丰富的悬浮按钮组件，支持多种位置、大小和自定义样式。

## 基本用法

```vue
<template>
  <FloatingButton @click="handleClick" />
</template>

<script setup>
import FloatingButton from '@/components/FloatingButton/index.vue'

function handleClick() {
  console.log('悬浮按钮被点击')
}
</script>
```

## 属性配置

| 属性名          | 类型          | 默认值                                   | 说明                                                                                   |
| --------------- | ------------- | ---------------------------------------- | -------------------------------------------------------------------------------------- |
| position        | String        | 'right-bottom'                           | 按钮位置（非拖拽模式），可选值：'right-bottom', 'right-top', 'left-bottom', 'left-top' |
| size            | String/Number | 'medium'                                 | 按钮大小，可选值：'small', 'medium', 'large' 或自定义数值（单位：rpx）                 |
| icon            | String        | 'add'                                    | 图标名称（wot-design-uni 图标）                                                        |
| iconColor       | String        | '#ffffff'                                | 图标颜色                                                                               |
| backgroundColor | String        | '#4d63e0'                                | 背景颜色                                                                               |
| customClass     | String        | ''                                       | 自定义样式类                                                                           |
| offset          | Object        | { x: 32, y: 32 }                         | 距离边缘的距离（非拖拽模式，单位：rpx）                                                |
| disabled        | Boolean       | false                                    | 是否禁用                                                                               |
| visible         | Boolean       | true                                     | 是否显示                                                                               |
| draggable       | Boolean       | false                                    | 是否可拖拽                                                                             |
| initialPosition | Object        | { x: 0, y: 0 }                           | 拖拽模式的初始位置（单位：rpx）                                                        |
| damping         | Number        | 20                                       | 拖拽阻尼系数                                                                           |
| friction        | Number        | 2                                        | 拖拽摩擦系数                                                                           |
| outOfBounds     | Boolean       | false                                    | 是否可以超出边界                                                                       |
| bounds          | Object        | { top: 0, right: 0, bottom: 0, left: 0 } | 可拖拽区域的边界（单位：rpx）                                                          |

## 事件

| 事件名         | 说明               | 回调参数                 |
| -------------- | ------------------ | ------------------------ |
| click          | 点击按钮时触发     | -                        |
| positionChange | 拖拽位置变化时触发 | { x: number, y: number } |
| dragStart      | 开始拖拽时触发     | -                        |
| dragEnd        | 拖拽结束时触发     | -                        |

## 插槽

| 插槽名  | 说明                           |
| ------- | ------------------------------ |
| default | 自定义按钮内容，会替换默认图标 |

## 使用示例

### 基本用法

```vue
<FloatingButton @click="handleAdd" />
```

### 自定义位置和大小

```vue
<FloatingButton position="left-top" size="large" @click="handleClick" />
```

### 自定义图标和颜色

```vue
<FloatingButton icon="message" icon-color="#ffffff" background-color="#52c41a" @click="handleMessage" />
```

### 自定义偏移距离

```vue
<FloatingButton :offset="{ x: 48, y: 48 }" @click="handleClick" />
```

### 使用插槽自定义内容

```vue
<FloatingButton @click="handleClick">
  <view class="custom-content f-xy-center">
    <text class="fon-S24 color-white">+</text>
  </view>
</FloatingButton>
```

### 禁用状态

```vue
<FloatingButton :disabled="true" @click="handleClick" />
```

### 控制显示隐藏

```vue
<template>
  <view>
    <wd-button @click="toggleVisible"> {{ visible ? '隐藏' : '显示' }}按钮 </wd-button>

    <FloatingButton :visible="visible" @click="handleClick" />
  </view>
</template>

<script setup>
import { ref } from 'vue'

const visible = ref(true)

function toggleVisible() {
  visible.value = !visible.value
}

function handleClick() {
  console.log('按钮被点击')
}
</script>
```

### 自定义尺寸

```vue
<!-- 使用数值自定义尺寸（单位：rpx） -->
<FloatingButton :size="150" @click="handleClick" />
```

### 可拖拽按钮

```vue
<FloatingButton :draggable="true" :initial-position="{ x: 100, y: 200 }" :bounds="{ top: 50, right: 50, bottom: 50, left: 50 }" @click="handleClick" @position-change="handlePositionChange" />
```

### 拖拽事件处理

```vue
<template>
  <FloatingButton :draggable="true" @drag-start="onDragStart" @drag-end="onDragEnd" @position-change="onPositionChange" />
</template>

<script setup>
function onDragStart() {
  console.log('开始拖拽')
}

function onDragEnd() {
  console.log('拖拽结束')
}

function onPositionChange(position) {
  console.log('位置变化:', position)
}
</script>
```

## 样式定制

组件使用了项目的全局样式类，你可以通过 `customClass` 属性添加自定义样式：

```vue
<FloatingButton custom-class="my-custom-button" @click="handleClick" />
```

```css
.my-custom-button {
  /* 自定义样式 */
}
```

## 注意事项

1. 组件使用了 `wot-design-uni` 的图标组件，确保项目中已正确配置
2. 组件使用了项目的全局样式类（如 `f-xy-center`、`box-shadow` 等）
3. 非拖拽模式下组件具有固定定位，z-index 为 999
4. 支持深色模式自动适配
5. 在移动端避免使用 hover 效果，使用 active 状态代替
6. 拖拽功能使用了 uni-app 的 `movable-view` 组件，完全兼容微信小程序
7. 拖拽模式下的边界限制通过 `bounds` 属性设置，单位为 rpx
8. 自定义尺寸支持数值类型，单位为 rpx，适配不同屏幕尺寸
9. 拖拽过程中会自动区分点击和拖拽操作，短时间触摸会触发点击事件
