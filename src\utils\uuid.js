const hexList = []
for (let i = 0; i <= 15; i++) {
  hexList[i] = i.toString(16)
}
// 导出一个函数，用于生成UUID
export const buildUUID = () => {
  // 定义一个空字符串，用于存储生成的UUID
  let uuid = ''
  // 循环36次，生成UUID
  for (let i = 1; i <= 36; i++) {
    // 如果i等于9、14、19、24，则添加一个'-'字符
    if (i === 9 || i === 14 || i === 19 || i === 24) {
      uuid += '-'
      // 如果i等于15，则添加一个4字符
    } else if (i === 15) {
      uuid += 4
      // 如果i等于20，则添加一个随机生成的字符
    } else if (i === 20) {
      uuid += hexList[(Math.random() * 4) | 8]
      // 否则，添加一个随机生成的字符
    } else {
      uuid += hexList[(Math.random() * 16) | 0]
    }
  }
  // 返回生成的UUID，并去掉其中的'-'字符
  return uuid.replace(/-/g, '')
}

let unique = 0
// 导出一个函数，用于生成短UUID
export const buildShortUUID = (prefix = '') => {
  // 获取当前时间戳
  const time = Date.now()
  // 生成一个随机数
  const random = Math.floor(Math.random() * 1000000000)
  // 唯一值自增
  unique++
  // 返回拼接后的字符串
  return prefix + '_' + random + unique + String(time)
}
