<template>
  <view class="all" id="DOMID" />
  <view
    :data
    :center
    :marker
    :codes
    :isShow
    :isMapBaseShow
    :remove
    :change:codes="map.getFilterList"
    :change:isShow="map.changeFilter"
    :change:isMapBaseShow="map.changeMapBaseShow"
    :change:data="map.updatedDot"
    :change:center="map.reach"
    :change:marker="map.addMarker"
    :change:remove="map.removeMarker"
  />
</template>

<script>
import { getZoneData } from '/src/services/model/map.js'
import uniUtil from '/src/utils/uniUtil'
export default {
  methods: {
    async add(value) {
      try {
        const result = await getZoneData(value.Zone_Code)
        const [res] = JSON.parse(result.data)
        uniUtil.navigateTo(`/src/pages/mine/children/material/detail?id=${res.Client_Name}`)
      } catch (error) {
        uniUtil.showToast(error)
      }
    }
  }
}
</script>

<script setup>
import { ref, onMounted } from 'vue'
import { getSQL } from '/src/services/model/material'

defineProps({
  data: Array,
  isShow: { type: Boolean, default: false },
  isMapBaseShow: { type: Boolean, default: true }
})

const center = ref(null)
const marker = ref(null)
const codes = ref(null)
const remove = ref(0)
const reach = (val) => (center.value = val)
const addMarker = (val) => {
  marker.value = val
  setTimeout(() => (marker.value = null))
}
onMounted(() => {
  setTimeout(async () => {
    try {
      const res = await getSQL('SELECT Zone_Code FROM [dbo].[DIST_ADDRESS]  where Client_Name is not NULL')
      codes.value = JSON.parse(res.data).map((item) => item.Zone_Code)
    } catch (error) {
      uniUtil.showToast(error)
    }
  })
})

const removeMarker = () => remove.value++

defineExpose({
  reach,
  addMarker,
  removeMarker
})
</script>

<script module="map" lang="renderjs">
import mapboxgl from 'mapbox-gl'
import basicsConfig from './config.js'
import uniUtil from '../../utils/uniUtil.js'

export default {
  data() {
    return { Map: null, Marker: null, filterList: null }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    // 初始化地图
    initMap(options = {}) {
      const accessToken = options?.accessToken ?? basicsConfig.map.accessToken
      const mapConfig = options?.map ?? {}
      mapboxgl.accessToken = accessToken
      const mapOptions = { container: 'DOMID', ...basicsConfig.map, ...mapConfig }
      this.Map = new mapboxgl.Map(mapOptions)
      this.Map.on('load', () => {
        this.loadingImages({
          总阀: 'https://www.szwgft.cn:8090/AppIcon/icon1.png',
          总表: 'https://www.szwgft.cn:8090/AppIcon/icon2.png',
          已处理: 'https://www.szwgft.cn:8090/AppIcon/1.png',
          未处理: 'https://www.szwgft.cn:8090/AppIcon/0.png',
          异常: 'https://www.szwgft.cn:8090/AppIcon/marker0.png',
          临供: 'https://www.szwgft.cn:8090/AppIcon/marker1.png',
          切换: 'https://www.szwgft.cn:8090/AppIcon/marker2.png',
          初验: 'https://www.szwgft.cn:8090/AppIcon/marker3.png'
        })
        this.addDotLayer()
        this.addZoneFill2Event()
      })
    },
    loadingImages(imagesOptions) {
      const images = Object.entries(imagesOptions)
      images.forEach(([imageName, imageUrl]) => this.Map.loadImage(imageUrl, (error, image) => this.Map.addImage( imageName, image)))
    },

    // 添加点
    addDotLayer() {
      this.Map.addSource('dot-source', {
        type: 'geojson',
        data: { type: 'FeatureCollection', features: [] }
      })
      this.Map.addLayer({ id: 'point', type: 'symbol', source: 'dot-source', layout: { 'icon-image': ['get', 'icon'], 'icon-size': 0.3,'icon-allow-overlap': false, 'icon-offset': [0, -50] } })
      this.Map.on('click', 'point', (e) => {
        const [feature] = this.Map.queryRenderedFeatures(e.point, { layers: ['point'] })
        const { properties } = feature
        uniUtil.navigateTo(properties.path)
      })
    },
    // 更新数据
    updatedDot(data) {
      if (!data.length) return
      const source = this.Map.getSource('dot-source')
      if(!source) return
      const updatedGeojsonData = { type: 'FeatureCollection', features: data }
      source.setData(updatedGeojsonData)
    },
    reach(data) {
      if (!data) return
      this.Map.flyTo({ center: data.Center_Point, zoom: 15, curve: 1.5, duration: 2500 })
      this.Map.setFilter('zone-line2', ['match', ['get', 'Zone_Name'], data.name, true, false])
    },
    addMarker(data, a, ownerVm) {
      if (!data) return

      if (this.Marker) {
        this.Marker.remove()
        this.Map.off('move', this.handlerMoveEvent)
      }

      // 监听地图拖动事件
      this.Map.on('move', handlerMoveEvent)
      // 缓存函数以便清除
      this.handlerMoveEvent = handlerMoveEvent
      // 获取中心点坐标
      let { lng, lat } = this.Map.getCenter()
      // 添加标记
      this.Marker = new mapboxgl.Marker({ color: data.color }).setLngLat([lng, lat]).addTo(this.Map)
      // 标记添加点击事件
      this.Marker.getElement().addEventListener('click', handlerMarkerClick)
      const thia = this
      // 地图拖动事件处理函数
      function handlerMoveEvent() {
        // 获取新的中心点坐标
        let { lng, lat} = thia.Map.getCenter()
        // 跟新标记位置
        thia.Marker.setLngLat([lng, lat])
      }
      // 标记点击事件处理函数
      function handlerMarkerClick(event) {
        event.stopPropagation()
        thia.Map.off('move', handlerMoveEvent)
        // 查询区块要素
        const [feature] = thia.Map.queryRenderedFeatures([thia.Marker._pos.x, thia.Marker._pos.y], { layers: ['zone-fill']})
        const { Zone_Code, Zone_Name } = feature?.properties ?? { Zone_Code: '超出边界', Zone_Name: '超出边界' }

        uniUtil.navigateTo( `${data.path}?lngLat=${JSON.stringify(thia.Marker._lngLat)}&code=${Zone_Code}&name=${Zone_Name}`)
        thia.Marker.getElement().removeEventListener('click', handlerMarkerClick)
      }
    },
    removeMarker() {
      if (!this.Marker) return
      this.Marker.remove()
    },
    // 控制小区档案图层展示
    changeFilter(value) {
      if (!this.filterList) return
      const filter = value ? ['in', 'Zone_Code', ...this.filterList] : ['has', 'zone_Code']
      this.Map.setFilter('zone-fill2', filter)
    },
    getFilterList(list) {
      if(!list) return
      this.filterList = list
    },
    //小区档案图层点击事件处理函数
    addZoneFill2Event(){
      this.Map.on('click', 'zone-fill2', (e) => {
        const { x, y } = e.point
        const [feature] = this.Map.queryRenderedFeatures([x, y], { layers: ['zone-fill2'] })
        this.$ownerInstance.callMethod('add', feature.properties)
      })
    },
    // 图层展示控制函数
    changeMapBaseShow(type) {
      if(type === null || type === undefined) return
      const filter = type ? ['has', 'Zone_Code'] : ['has', 'zone_Code']
      this.Map.setFilter('zone-fill', filter)
      this.Map.setFilter('zone-line', filter)
      this.Map.setFilter('zone-symbol', filter)
      if(!type){
        this.Map.setFilter('zone-line2', filter)
        this.Map.setFilter('FTModificationWorks_fill', null)
        this.Map.setFilter('FTModificationWorks_symbol',null)
      }else{
        this.Map.setFilter('FTModificationWorks_fill', ['has', 'Zone_Code'])
        this.Map.setFilter('FTModificationWorks_symbol', ['has', 'Zone_Code'])
      }
    }
  }
}
</script>
