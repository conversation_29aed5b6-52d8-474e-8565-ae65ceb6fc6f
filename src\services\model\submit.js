import { nodeRequest } from '../index'

// 模糊查询区块
export const getZoneDataLike = (ZoneName) => nodeRequest.get(`/api/api/Data/ZoneData_like?Zone_Name=${ZoneName}`)

//gis问题上报
export const createRecordApi = (data) => nodeRequest.post(`/api/api/OperationFeedback`, data)
// 修改
export const alterRecordApi = (data, id) => nodeRequest.put(`/api/api/OperationFeedback?id=${id}`, data)
// gis维护列表
export const getRecordList = (station) => nodeRequest.get(`/api/api/OperationFeedback/station?station=${station}`, null, true)
// 删除
export const deleteOperationFeedbackApi = (id) => nodeRequest.delete(`/api/api/OperationFeedback?id=${id}`)
// 详情
export const getOperationFeedbackDetailApi = (id) => nodeRequest.get(`/api/api/OperationFeedback/id?id=${id}`)
export const getOperationFeedbackZoneCodelApi = (id) => nodeRequest.get(`/api/api/OperationFeedback/Zone_Code?Zone_Code=${id}`)

// 外业上报
// 新增
export const createValveVerificationApi = (data) => nodeRequest.post(`/api/api/ValveVerification`, data)
// 更新
export const alterValveVerificationApi = (data, id) => nodeRequest.put(`/api/api/ValveVerification?id=${id}`, data)
// 删除
export const deleteValveVerificationApi = (id) => nodeRequest.delete(`/api/api/ValveVerification?id=${id}`)
// 详情
export const getValveVerificationDetailApi = (id) => nodeRequest.get(`/api/api/ValveVerification/Id?id=${id}`)

// 上报列表
export const getalveVerificationListApi = (ZoneCode) => {
  if (ZoneCode) {
    return nodeRequest.get(`/api/api/ValveVerification/Zone_Code?Zone_Code=${ZoneCode}`, null, true)
  } else {
    return nodeRequest.get(`/api/api/ValveVerification/Zone_Code`, null, true)
  }
}
