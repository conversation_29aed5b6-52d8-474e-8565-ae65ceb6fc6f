const zoneColor = {
  梅林片区: '#1677ff',
  景田片区: 'red',
  香蜜片区: '#fa9600',
  福东南片区: '#0000FF',
  福东北片区: '#ffd447',
  莲花片区: '#bdfb9b',
  中心城片区: '#ffb4a6',
  福保片区: '#9f4fff',
  福民片区: '#00ffff',
  新洲片区: '#00ff00'
}
function transitionArr(category, rest) {
  const entries = Object.entries(category)
  const entriesArr = entries.flat(1)
  if (rest) entriesArr.push(rest)
  return entriesArr
}

// 基础默认配置
const basicsConfig = {
  // 初始化地图默认基础配置
  map: {
    accessToken: 'pk.eyJ1Ijoibm9ydGh2aSIsImEiOiJjbGVydjM1OXYwMnpkM3BxZGw5Ynlrbm13In0.queCazXHMgl8WHfZ1lF4xg',
    center: [114.05528061331722, 22.54140197444606, 0], //中心点
    zoom: 13, //默认缩放
    minZoom: 10, //最小缩放
    maxZoom: 22, //最大缩放
    projection: 'globe', //球形地图
    antialias: true, //抗锯齿, //球形地图
    style: {
      version: 8,
      sources: {
        // 天地图矢量
        'osm-tiles1': {
          type: 'raster',
          tiles: ['https://t4.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'],
          tileSize: 256,
          maxzoom: 18
        },
        'osm-tiles2': {
          type: 'raster',
          tiles: ['https://t4.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'],
          tileSize: 256,
          maxzoom: 18
        },
        // 无缝区块
        address_source: {
          type: 'vector',
          scheme: 'tms',
          tiles: ['https://www.szwgft.cn:8889/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ZONE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf']
        },
        // 片区图层
        FTModificationWorks: {
          type: 'vector',
          tiles: ['https://www.szwgft.cn:8889/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ModificationWorks@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf'],
          scheme: 'tms'
        }
      },
      glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
      layers: [
        // 天地图底图
        {
          id: 'simple-tiles1',
          type: 'raster',
          source: 'osm-tiles1'
        },
        {
          id: 'simple-tiles2',
          type: 'raster',
          source: 'osm-tiles2'
        },
        // 无缝区块
        {
          id: 'zone-fill',
          type: 'fill',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          paint: {
            'fill-opacity': 0.35,
            'fill-color': [
              'case',
              ['==', ['get', 'Type'], '市政路'],
              '#999',
              ['==', ['get', 'Type'], '其他'],
              '#888',
              [
                'match',
                ['get', 'ManagerNam'], // type 等于 小区，根据 ManagerNam 设置颜色
                ['福中水务所'],
                '#ee0000',
                ['福东水务所'],
                '#bdfb9b',
                ['梅林水务所'],
                '#1677ff',
                ['香蜜水务所'],
                '#fa9600',
                '#999' // 默认颜色，如果 ManagerNam 不匹配以上任何值
              ]
            ]
          }
        },
        {
          id: 'zone-fill2',
          type: 'fill',
          'source-layer': 'FT_ZONE',
          filter: ['has', 'zone_Code'],
          source: 'address_source',
          paint: {
            'fill-opacity': 0.6,
            'fill-outline-color': '#000000',
            paint: {
              'fill-opacity': 0.35,
              'fill-color': [
                'case',
                ['==', ['get', 'Type'], '市政路'],
                '#999',
                ['==', ['get', 'Type'], '其他'],
                '#888',
                [
                  'match',
                  ['get', 'ManagerNam'], // type 等于 小区，根据 ManagerNam 设置颜色
                  ['福中水务所'],
                  '#ee0000',
                  ['福东水务所'],
                  '#bdfb9b',
                  ['梅林水务所'],
                  '#1677ff',
                  ['香蜜水务所'],
                  '#fa9600',
                  '#999' // 默认颜色，如果 ManagerNam 不匹配以上任何值
                ]
              ]
            }
          }
        },
        {
          id: 'zone-line',
          type: 'line',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          paint: {
            'line-width': 1,
            'line-color': '#343434'
          }
        },
        {
          id: 'zone-line2',
          type: 'line',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          filter: ['==', ['get', 'Zone_Name'], ''],
          paint: {
            'line-width': 5,
            'line-color': '#07c160'
          }
        },
        {
          id: 'zone-symbol',
          type: 'symbol',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          layout: {
            'text-field': ['get', 'Zone_Name'],
            'text-anchor': 'center',
            'text-size': 10
          },
          paint: {
            'text-color': 'black',
            'text-halo-color': 'white',
            'text-halo-width': 1
          }
        },
        // 片区图层
        {
          id: 'FTModificationWorks_fill',
          type: 'fill',
          'source-layer': 'FT_ModificationWorks',
          source: 'FTModificationWorks',
          filter: ['has', 'Zone_Code'],
          paint: {
            'fill-opacity': 0.35,
            'fill-outline-width': 1,
            'fill-outline-color': '#000000',
            'fill-color': ['match', ['get', 'AreaName'], ...transitionArr(zoneColor, 'pink')]
          }
        },
        {
          id: 'FTModificationWorks_symbol',
          'source-layer': 'FT_ModificationWorks',
          type: 'symbol',
          filter: ['has', 'Zone_Code'],
          source: 'FTModificationWorks',
          layout: { 'text-field': ['get', 'AreaName'], 'text-size': 10 },
          paint: { 'text-color': 'black', 'text-halo-color': 'white', 'text-halo-width': 1 }
        }
      ]
    }
  }
}

export default basicsConfig
