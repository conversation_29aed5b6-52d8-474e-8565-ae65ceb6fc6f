import { ref } from 'vue'
import { defineStore } from 'pinia'
export const useCommonStore = defineStore('common', commonCallBack)

function commonCallBack() {
  const screenInformation = ref(null) // 屏幕信息
  const equipmentInformation = ref(null) // 设备信息
  function getUserInformation() {
    uni.getSystemInfo({ success: (res) => (screenInformation.value = res) })
    equipmentInformation.value = uni.getDeviceInfo()
  }

  return { equipmentInformation, getUserInformation, screenInformation }
}
