import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getRecordList, getalveVerificationListApi } from '/src/services/model/submit.js'
import { getSecondaryWaterProgressList } from '/src/services/model/secondSorce.js'

import uniUtil from '/src/utils/uniUtil.js'

export const useMapDotStore = defineStore('mapDot', () => {
  // 外业数据列表
  const fieldRecords = ref([])
  // 获取外业数据列表
  async function getFieldRecords() {
    const result = await getalveVerificationListApi()
    const data = result?.data?.valveVerification ?? []
    fieldRecords.value = data.map((item, index) => settleData(item, index, 'field'))
  }

  // gis错误数据列表
  const gisFaults = ref([])
  // 获取gis错误数据列表
  async function getGisFaults() {
    const { station } = uniUtil.get('userInfo')
    const result = await getRecordList(station)
    const data = result?.data?.operationFeedbacks.reverse() ?? []
    gisFaults.value = data.map((item, index) => settleData(item, index, 'gis'))
  }

  const secondSources = ref([])
  async function getSecondSourceList() {
    const res = await getSecondaryWaterProgressList()
    const data = res.data.secondaryWaterProgress.reverse() || []
    secondSources.value = data.map((item, index) => settleData(item, index, 'second'))
  }

  // 整理数据
  function settleData(value, index, type) {
    let coordinates
    value.iconSize = 0.3
    if (type === 'gis') {
      value.icon = value.path4.length > 0 ? '已处理' : '未处理'
      value.path = `/src/pages/mine/children/record/detail?id=${value.id}`
      coordinates = [value.x, value.y]
    } else if (type == 'field') {
      value.icon = value.point_Type
      value.path = `/src/pages/mine/children/field-work/detail?id=${value.id}`
      coordinates = value.coordinates.split(',')
    } else {
      const current = value.current
      value.isCollect = value.accuratePosition && value.accuratePosition.length ? '已完成' : '未完成'
      // value.icon = value.progressStatus != '正常' ? '异常' : current < 10 ? '临供' : current < 14 ? '切换' : '初验'
      value.iconSize = 0.08

      if (value.progressStatus !== '正常') {
        value.icon = '异常'
      } else if (current < 4) {
        value.icon = '临供前'
      } else if (current < 12) {
        value.icon = '临供'
      } else if (current < 14) {
        value.icon = '切换'
      } else {
        value.icon = '初验'
      }
      value.path = `/src/pages/mine/children/second-source/detail?id=${value.id}`
      coordinates = [value.x, value.y]
    }

    return { type: 'Feature', id: index, properties: value, geometry: { type: 'Point', coordinates } }
  }

  return { fieldRecords, getFieldRecords, gisFaults, getGisFaults, secondSources, getSecondSourceList }
})

export default useMapDotStore
