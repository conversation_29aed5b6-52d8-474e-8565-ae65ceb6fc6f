import { nodeRequest } from '../index'

class Maintain {
  statistics() {
    return nodeRequest.get(`/nodeServer/maintain/statistics`)
  }
  list(finish, page = 1, pageSize = 15) {
    if (finish === undefined) {
      return nodeRequest.get(`/nodeServer/maintain/list?page=${page}&pageSize=${pageSize}`)
    } else if (finish == 'true') {
      return nodeRequest.get(`/nodeServer/maintain/list?finish=true&page=${page}&pageSize=${pageSize}`)
    } else if (finish == 'false') {
      return nodeRequest.get(`/nodeServer/maintain/list?finish=false&page=${page}&pageSize=${pageSize}`)
    } else {
      return nodeRequest.get(`/nodeServer/maintain/list?finish=all`)
    }
  }
}

export const MaintainApi = new Maintain()
