import { nodeRequest } from '../index'

// 登录
export const login = (data) => nodeRequest.post(`/api/api/User/login`, data)
// 获取用户信息
export const getUserInfo = (token) => nodeRequest.get(`/api/api/User/Getuser?token=${token}`)
export const loginFace = (data) => nodeRequest.post('/api/api/User/login_APP_face', data)
export const updatePassword = (token, data) => nodeRequest.post(`/api/api/User/login_update?Token=${token}`, data)
