<template>
  <div class="all">
    <view class="all" id="DOMID1" />
    <view :center :MarkerCenter :change:MarkerCenter="map.defaultMarkerCenter" :change:center="map.reachfn" />
  </div>
</template>

<script>
export default {
  emits: ['handler'],
  props: {
    MarkerCenter: { type: Array }
  },
  data() {
    return {
      center: null
    }
  },
  methods: {
    reach(center) {
      this.center = center
    },
    reachMarkerCenter(center) {
      this.MarkerCenter = center
    },
    handler(data) {
      this.$emit('handler', data)
    }
  }
}
</script>

<script module="map" lang="renderjs">
import mapboxgl from 'mapbox-gl'
import basicsConfig from './config.js'
import uniUtil from '../../../../../../../utils/uniUtil.js'

export default {
  data() {
    return {
      Map: null,
      Marker: null,
      MarkerCenter:[]
    }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    initMap() {
      mapboxgl.accessToken = basicsConfig.map.accessToken
      const mapOptions = {
        container: 'DOMID1',
        ...basicsConfig.map,
      }
      this.Map = new mapboxgl.Map(mapOptions)
      this.Map.on('load', () => this.addMarkerfn())
    },
    reachfn(data) {
      if (!data) return
      this.Map.flyTo({
        center: data.Center_Point,
        zoom: 17.8,
        curve: 1.5,
        duration: 2500
      })
      if(data.name){
        this.Map.setFilter('zone-line2', ['match', ['get', 'Zone_Name'], data.name, true, false])
      }
    },
    defaultMarkerCenter(center){

      if(!center) return
      this.MarkerCenter = center
      setTimeout(() => {
        this.Map.setCenter(center)
      }, 100);
    },

    addMarkerfn() {
      if (this.Marker) {
        this.Marker.remove()
        this.Map.off('move', this.handlerMoveEvent)
      }

      // 监听地图拖动事件
      this.Map.on('move', handlerMoveEvent)
      // 缓存函数以便清除
      this.handlerMoveEvent = handlerMoveEvent
      // 获取中心点坐标
      let { lng, lat } =  this.Map.getCenter()
      // 添加标记
      this.Marker = new mapboxgl.Marker().setLngLat([ lng, lat ]).addTo(this.Map)
      // 标记添加点击事件
      this.Marker.getElement().addEventListener('click', handlerMarkerClick)
      const thia = this
      // 地图拖动事件处理函数
      function handlerMoveEvent() {
        // 获取新的中心点坐标
        let { lng, lat } = thia.Map.getCenter()
        // 跟新标记位置
        thia.Marker.setLngLat([lng, lat])
      }
      // 标记点击事件处理函数
      async function handlerMarkerClick(event) {
        event.stopPropagation()
        // thia.Map.off('move', handlerMoveEvent)
        const obj = {x:thia.Marker._lngLat.lng, y:thia.Marker._lngLat.lat, code:null}

        const [feature] = thia.Map.queryRenderedFeatures([thia.Marker._pos.x, thia.Marker._pos.y], { layers: ['zone-fill'] })

        if(feature) {
          obj.code = feature.properties.Zone_Code
          obj.feature = feature.properties
        }

        thia.$ownerInstance.callMethod('handler',obj)
        // thia.Marker.getElement().removeEventListener('click', handlerMarkerClick)
      }
    }
  }
}
</script>
