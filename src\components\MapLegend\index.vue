<template>
  <view class="map-legend-container">
    <!-- 现代化图例卡片 -->
    <view class="legend-card" :class="{ 'card-collapsed': !isExpanded }">
      <!-- 卡片头部 - 可点击折叠 -->
      <view class="legend-header" @click="toggleExpanded">
        <view class="header-icon">
          <wd-icon name="chart-pie" size="24rpx" color="#1890ff" />
        </view>
        <text class="header-title">图例</text>

        <!-- 统计信息 -->
        <view v-if="!isExpanded && totalCount > 0" class="header-count">
          <text class="count-text">{{ totalCount }}</text>
        </view>

        <!-- 折叠按钮 -->
        <view class="toggle-button" :class="{ 'button-expanded': isExpanded }">
          <wd-icon :name="isExpanded ? 'arrow-up' : 'arrow-down'" size="20rpx" color="#1890ff" />
        </view>
      </view>

      <!-- 图例内容 - 可折叠 -->
      <view class="legend-content" :class="{ 'content-collapsed': !isExpanded }" :style="{ maxHeight: isExpanded ? contentHeight + 'rpx' : '0' }">
        <view class="content-wrapper">
          <view
            v-for="(item, index) in legend"
            :key="item.label"
            class="legend-item"
            :class="{ 'item-with-animation': isExpanded }"
            :style="{
              animationDelay: isExpanded ? `${index * 0.1}s` : '0s',
              transitionDelay: isExpanded ? `${index * 0.05}s` : `${(legend.length - index) * 0.05}s`
            }"
          >
            <!-- 图例图标 -->
            <view class="item-icon-wrapper">
              <view v-if="item.color" class="item-icon color-icon" :style="{ backgroundColor: item.color }">
                <view class="icon-shine"></view>
              </view>
              <view v-else class="item-icon image-icon">
                <image :src="item.url" class="icon-image" mode="aspectFit" />
                <view class="icon-border"></view>
              </view>
            </view>

            <!-- 图例信息 -->
            <view class="item-content">
              <view class="item-label">{{ item.label }}</view>
              <view v-if="item.value !== undefined || item.unit" class="item-stats">
                <text v-if="item.value !== undefined" class="stats-value">{{ item.value }}</text>
                <text v-if="item.unit" class="stats-unit">{{ item.unit }}</text>
              </view>
            </view>

            <!-- 装饰元素 -->
            <view class="item-decoration">
              <view class="decoration-dot"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 卡片装饰 -->
      <view class="card-decoration">
        <view class="decoration-circle circle-1"></view>
        <view class="decoration-circle circle-2"></view>
        <view class="decoration-line"></view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'

const props = defineProps({
  legend: Array,
  defaultExpanded: {
    type: Boolean,
    default: true // 默认折叠
  }
})

// 折叠状态
const isExpanded = ref(props.defaultExpanded)
const contentHeight = ref(0)

// 计算总数量
const totalCount = computed(() => {
  if (!props.legend || !Array.isArray(props.legend)) return 0
  return props.legend.reduce((total, item) => {
    if (item.value !== undefined && typeof item.value === 'number') {
      return total + item.value
    }
    return total
  }, 0)
})

// 切换展开/收起状态
function toggleExpanded() {
  isExpanded.value = !isExpanded.value
}

// 计算内容高度
function calculateContentHeight() {
  if (!props.legend || !Array.isArray(props.legend)) {
    contentHeight.value = 0
    return
  }

  // 更准确的高度计算
  // 图例项高度：图标48rpx + 上下padding 24rpx = 72rpx
  // 如果有统计信息，需要额外高度
  // 间距：16rpx
  // 容器padding：4rpx (content-wrapper的padding-top)

  let totalHeight = 4 // content-wrapper的padding-top

  props.legend.forEach((item, index) => {
    // 基础图例项高度
    let itemHeight = 72 // 图标 + padding

    // 如果有统计信息，增加高度
    if (item.value !== undefined || item.unit) {
      itemHeight += 24 // 统计信息行高度
    }

    totalHeight += itemHeight

    // 添加间距（除了最后一项）
    if (index < props.legend.length - 1) {
      totalHeight += 16
    }
  })

  // 添加一些缓冲空间，确保内容完全显示
  totalHeight += 20

  contentHeight.value = Math.max(totalHeight, 120)
}

// 监听 legend 变化，重新计算高度
watch(
  () => props.legend,
  () => {
    nextTick(() => {
      calculateContentHeight()
    })
  },
  { immediate: true, deep: true }
)

// 监听展开状态变化
watch(isExpanded, (newVal) => {
  if (newVal) {
    nextTick(() => {
      calculateContentHeight()
    })
  }
})
</script>

<style lang="scss" scoped>
/* 基础样式类 - 提高CSS复用性 */
.modern-card {
  position: relative;
  background: rgba(255, 255, 255, 0.88);
  backdrop-filter: blur(16rpx);
  border-radius: 20rpx;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.subtle-shadow {
  box-shadow: 0 6rpx 24rpx rgba(24, 144, 255, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(24, 144, 255, 0.08);
}

.smooth-transition {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.map-legend-container {
  position: relative;
  min-width: 200rpx;
  max-width: 300rpx;
}

/* 优化的图例卡片 - 降低视觉重量 */
.legend-card {
  @extend .modern-card;
  @extend .subtle-shadow;
  padding: 18rpx;
}

/* 折叠状态的卡片 */
.card-collapsed {
  .legend-header {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
}

/* 卡片头部 - 优化高度和间距 */
.legend-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding: 12rpx 8rpx;
  border-bottom: 1rpx solid rgba(24, 144, 255, 0.08);
  position: relative;
  border-radius: 12rpx;
  margin: -8rpx -8rpx 8rpx -8rpx;
  @extend .smooth-transition;
}

/* 移动端触摸反馈 - 移除hover效果 */
.legend-header:active {
  background: rgba(24, 144, 255, 0.08);
  transform: scale(0.98);
}

.header-icon {
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.2);
}

.header-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

/* 头部统计信息 - 优化尺寸 */
.header-count {
  background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
  border-radius: 10rpx;
  padding: 3rpx 10rpx;
  margin-right: 10rpx;
  box-shadow: 0 1rpx 4rpx rgba(24, 144, 255, 0.2);
}

.count-text {
  font-size: 18rpx;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.2;
}

/* 折叠按钮 - 优化移动端尺寸 */
.toggle-button {
  width: 36rpx;
  height: 36rpx;
  background: rgba(24, 144, 255, 0.08);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(24, 144, 255, 0.15);
  @extend .smooth-transition;
}

.toggle-button:active {
  background: rgba(24, 144, 255, 0.15);
  transform: scale(0.92);
}

.button-expanded {
  background: rgba(24, 144, 255, 0.12);
  border-color: rgba(24, 144, 255, 0.25);
}

/* 图例内容 - 优化动画 */
.legend-content {
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: top;
}

.content-collapsed {
  opacity: 0;
  transform: scaleY(0.85);
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding-top: 2rpx;
}

/* 图例项 - 优化高度和间距 */
.legend-item {
  display: flex;
  align-items: flex-start;
  padding: 10rpx 6rpx;
  position: relative;
  border-radius: 12rpx;
  opacity: 0;
  transform: translateY(16rpx);
  min-height: 64rpx;
  @extend .smooth-transition;
}

.item-with-animation {
  animation: smoothFadeInUp 0.7s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
}

/* 折叠时的动画 - 更平滑 */
.content-collapsed .legend-item {
  animation: smoothFadeOutDown 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
}

@keyframes smoothFadeInUp {
  0% {
    opacity: 0;
    transform: translateY(16rpx) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-2rpx) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes smoothFadeOutDown {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-8rpx) scale(0.95);
  }
}

/* 图例图标包装器 - 优化尺寸 */
.item-icon-wrapper {
  margin-right: 12rpx;
  position: relative;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.item-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  @extend .smooth-transition;
}

/* 颜色图标 - 减少阴影效果 */
.color-icon {
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.icon-shine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, transparent 50%);
  border-radius: 8rpx;
}

/* 图片图标 - 优化样式 */
.image-icon {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(24, 144, 255, 0.08);
}

.icon-image {
  width: 28rpx;
  height: 28rpx;
  z-index: 2;
  position: relative;
}

.icon-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 8rpx;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.08) 0%, rgba(82, 196, 26, 0.08) 100%);
}

/* 图例内容 - 优化间距和高度 */
.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
  min-height: 40rpx;
  justify-content: flex-start;
}

.item-label {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  word-wrap: break-word;
}

.item-stats {
  display: flex;
  align-items: center;
  gap: 6rpx;
  margin-top: 1rpx;
}

.stats-value {
  font-size: 22rpx;
  font-weight: 600;
  color: #1890ff;
  line-height: 1.3;
}

.stats-unit {
  font-size: 18rpx;
  color: #666;
  line-height: 1.3;
}

/* 装饰元素 - 简化设计 */
.item-decoration {
  margin-left: 8rpx;
  flex-shrink: 0;
  margin-top: 16rpx;
}

.decoration-dot {
  width: 6rpx;
  height: 6rpx;
  background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
  border-radius: 50%;
  opacity: 0.5;
  @extend .smooth-transition;
}

/* 卡片装饰 - 简化装饰效果 */
.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
  opacity: 0.6;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.03) 0%, rgba(82, 196, 26, 0.03) 100%);
  animation: gentleFloat 12s ease-in-out infinite;
}

.circle-1 {
  width: 60rpx;
  height: 60rpx;
  top: -30rpx;
  right: -15rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 40rpx;
  height: 40rpx;
  bottom: -20rpx;
  left: -8rpx;
  animation-delay: 6s;
}

.decoration-line {
  position: absolute;
  top: 50%;
  right: -15rpx;
  width: 30rpx;
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(24, 144, 255, 0.1) 50%, transparent 100%);
  transform: translateY(-50%);
  animation: gentlePulse 6s ease-in-out infinite;
}

@keyframes gentleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-6rpx) rotate(90deg);
    opacity: 0.4;
  }
}

@keyframes gentlePulse {
  0%,
  100% {
    opacity: 0.1;
    transform: translateY(-50%) scaleX(1);
  }
  50% {
    opacity: 0.3;
    transform: translateY(-50%) scaleX(1.1);
  }
}

/* 响应式设计 - 优化小屏幕适配 */
@media (max-width: 750rpx) {
  .map-legend-container {
    min-width: 180rpx;
    max-width: 260rpx;
  }

  .legend-card {
    padding: 16rpx;
    border-radius: 18rpx;
  }

  .header-icon {
    width: 36rpx;
    height: 36rpx;
    margin-right: 10rpx;
  }

  .header-title {
    font-size: 24rpx;
  }

  .toggle-button {
    width: 32rpx;
    height: 32rpx;
  }

  .header-count {
    padding: 2rpx 8rpx;
  }

  .count-text {
    font-size: 16rpx;
  }

  .legend-item {
    min-height: 56rpx;
    padding: 8rpx 4rpx;
  }

  .item-icon {
    width: 36rpx;
    height: 36rpx;
  }

  .icon-image {
    width: 24rpx;
    height: 24rpx;
  }

  .item-content {
    min-height: 36rpx;
    gap: 3rpx;
  }

  .item-label {
    font-size: 22rpx;
    line-height: 1.3;
  }

  .stats-value {
    font-size: 20rpx;
  }

  .stats-unit {
    font-size: 16rpx;
  }

  .item-decoration {
    margin-top: 14rpx;
  }

  .decoration-dot {
    width: 5rpx;
    height: 5rpx;
  }
}

/* 深色模式支持 - 优化对比度 */
@media (prefers-color-scheme: dark) {
  .modern-card {
    background: rgba(30, 30, 30, 0.88);
  }

  .subtle-shadow {
    border-color: rgba(255, 255, 255, 0.08);
  }

  .legend-header {
    border-bottom-color: rgba(255, 255, 255, 0.08);
  }

  .header-title {
    color: #fff;
  }

  .item-label {
    color: #fff;
  }

  .stats-unit {
    color: #ccc;
  }

  .image-icon {
    background: rgba(45, 45, 45, 0.85);
    border-color: rgba(255, 255, 255, 0.08);
  }
}

/* 移动端优化 - 完全移除hover效果，优化触摸反馈 */
@media (pointer: coarse) {
  /* 移动端点击反馈 - 更好的触摸体验 */
  .legend-header:active {
    background: rgba(24, 144, 255, 0.12);
    transform: scale(0.98);
  }

  .toggle-button:active {
    background: rgba(24, 144, 255, 0.25);
    transform: scale(0.88);
  }

  .legend-item:active {
    background: rgba(24, 144, 255, 0.06);
    transform: scale(0.98);
  }
}
</style>
