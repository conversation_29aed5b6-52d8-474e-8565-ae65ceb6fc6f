import { imageBaseUrl } from '/src/config/index.json'

// 时间转换
export function getCurrentDateTime() {
  const now = new Date()

  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')

  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const milliseconds = String(now.getMilliseconds()).padStart(3, '0')

  const timezoneOffset = -now.getTimezoneOffset()
  const offsetHours = Math.floor(Math.abs(timezoneOffset) / 60)
    .toString()
    .padStart(2, '0')
  const offsetMinutes = (Math.abs(timezoneOffset) % 60).toString().padStart(2, '0')
  const timezone = (timezoneOffset >= 0 ? '+' : '-') + offsetHours + ':' + offsetMinutes

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}${timezone}`
}

// 图片拼接地址
export function handlerImage(url) {
  return imageBaseUrl + url
}

// export function timestampToLocaleDateString(input) {
//   console.log(input)

//   // 检查输入是否是时间戳
//   let timestampPattern = /^\d{13}$/
//   if (timestampPattern.test(input)) {
//     // 如果是时间戳，则转换为日期字符串
//     let date = new Date(parseInt(input))
//     let isoString = date.toISOString()
//     return isoString
//   } else {
//     // 如果不是时间戳，则直接返回输入
//     return input
//   }
// }

function isTimestamp(value) {
  // 检查是否为数字且长度为10或13（秒或毫秒）
  return typeof value === 'number' && (value.toString().length === 10 || value.toString().length === 13)
}

export function timestampToLocaleDateString(value) {
  if (isTimestamp(value)) {
    const date = new Date(value)

    // 获取时区偏移（以分钟为单位）
    const timezoneOffset = date.getTimezoneOffset() * 60000

    // 调整时间戳以考虑时区偏移
    const adjustedDate = new Date(value - timezoneOffset)

    const formattedDate = adjustedDate.toISOString().split('T')[0] + 'T00:00:00'
    return formattedDate
  } else {
    return value
  }
}

export function mapNavigation({ lat, lng, name }) {
  if (!lat || !lng || !name) return uni.showToast({ title: '缺少参数', icon: 'none' })
  const buttons = [
    { title: '高德地图', name: 'amap', androidName: 'com.autonavi.minimap', iosName: 'iosamap://' },
    // { title: '百度地图', name: 'baidumap', androidName: 'com.baidu.BaiduMap', iosName: 'baidumap://' },
    { title: '腾讯地图', name: 'qqmap', androidName: 'com.tencent.map', iosName: 'qqmap://' }
  ]

  const mapP = {
    amap: `amapuri://route/plan/?sid=&did=&dlat=${lat}&dlon=${lng}&dname=${name}&dev=0&t=0`,
    qqmap: `qqmap://map/routeplan?type=drive&to=${name}&tocoord=${lat},${lng}&referer=fuxishan_uni_client`
    // baidumap: `baidumap://map/direction?origin=${P.selfLocation.latitude},${P.selfLocation.longitude}&destination=name:${P.name}|latlng:${P.latitude},${P.longitude}&coord_type=wgs84&mode=driving&src=andr.baidu.openAPIdemo"`
  }

  // #ifdef APP-PLUS
  plus.nativeUI.actionSheet({ title: '选择地图应用', cancel: '取消', buttons }, (e) => {
    const M = buttons[e.index - 1]
    if (plus.runtime.isApplicationExist({ pname: M.androidName })) {
      const url = encodeURI(mapP[M.name])
      plus.runtime.openURL(url, (e) => uni.showToast({ title: '打开失败', icon: 'none' }))
    } else {
      uni.showToast({ title: '请先安装' + M.title, icon: 'none' })
      return
    }
  })

  // #endif
}
