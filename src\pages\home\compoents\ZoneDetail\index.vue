<template>
  <div class="list">
    <div class="flex">
      <div class="f-1">
        <span>所属水务：</span><span>{{ detail.ManagerName ?? ' --' }}</span>
      </div>
      <div class="f-1">
        <span>所属街道：</span><span>{{ detail.Subdistrict ?? ' --' }}</span>
      </div>
    </div>

    <div class="flex">
      <div class="f-1">
        <span>所属社区：</span><span>{{ detail.Community ?? ' --' }}</span>
      </div>
      <div class="f-1">
        <span>区块类别：</span><span>{{ detail.Zone_Type ?? ' --' }}</span>
      </div>
    </div>

    <div class="flex">
      <div class="f-1">
        <span>所属路段：</span><span>{{ detail.Road ?? ' --' }}</span>
      </div>
      <div class="f-1">
        <span>优饮批次：</span><span>{{ detail.GoodDrinkBatch ?? ' --' }}</span>
      </div>
    </div>

    <div class="flex">
      <div class="f-1">
        <span>优饮完成时间：</span><span>{{ detail.GoodDrinkingFinishTime ?? ' --' }}</span>
      </div>
      <div class="f-1">
        <span>二供批次：</span><span>{{ detail.SecondarySupplylot ?? ' --' }}</span>
      </div>
    </div>

    <div class="flex">
      <div class="f-1">
        <span>二供情况：</span><span>{{ detail.SecondarySupplyCondition ?? ' --' }}</span>
      </div>
      <div class="f-1">
        <span>管网风险分级：</span><span>{{ detail.PipeNetworkRiskClassification ?? ' --' }}</span>
      </div>
    </div>

    <div class="flex">
      <div class="f-1">
        <span>二供风险分级：</span><span>{{ detail.SecondaryRiskClassification ?? ' --' }}</span>
      </div>
      <div class="f-1">
        <span>水质投诉分级：</span><span>{{ detail.WaterQualityComplaint ?? ' --' }}</span>
      </div>
    </div>
  </div>

  <scroll-view class="W100" scroll-x="true" scroll-with-animation :scrollLeft @touchstart.stop="" @touchmove.stop="" @touchend.stop="">
    <div style="width: 130%">
      <wd-segmented :options="list" v-model:value="current" />
    </div>
  </scroll-view>
</template>

<script setup>
import { ref, computed } from 'vue'
const props = defineProps({ detail: Object, show: Number, list: Array })
const current = defineModel({ default: '简介' })
const height = computed(() => (props.show === 1 ? '0px' : '300rpx'))
const scrollLeft = computed(() => {
  if (current.value === '简介') return 0
  if (current.value === '二供') return 0
  if (current.value === '档案') return 45
  if (current.value === '总阀总表') return 97
  if (current.value === '日常维护') return 97
})
</script>

<style lang="less" scoped>
.list {
  height: v-bind(height);
  overflow: hidden;
  transition: all 0.5s;
  & > * {
    margin-bottom: 12rpx;
    & > * {
      & > *:nth-child(1) {
        font-size: 24rpx;
      }
      & > *:nth-child(2) {
        font-size: 28rpx;
        font-weight: 700;
      }
    }
  }
}
</style>
