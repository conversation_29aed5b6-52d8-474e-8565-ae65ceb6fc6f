<template>
  <view class="example-page H100vh back-f5f5f5">
    <view class="header back-white box-shadow pad-24 mar-B24">
      <text class="fon-S36 fon-W600 color-333">悬浮按钮组件示例</text>
    </view>

    <view class="content pad-24">
      <view class="section mar-B48">
        <text class="section-title fon-S30 fon-W600 color-333 mar-B24">基本用法</text>
        <view class="demo-item back-white border-R12 pad-24 mar-B24">
          <text class="fon-S26 color-666">默认悬浮按钮（右下角）</text>
        </view>
      </view>

      <view class="section mar-B48">
        <text class="section-title fon-S30 fon-W600 color-333 mar-B24">不同位置</text>
        <view class="demo-item back-white border-R12 pad-24 mar-B24">
          <text class="fon-S26 color-666">查看四个角落的悬浮按钮</text>
        </view>
      </view>

      <view class="section mar-B48">
        <text class="section-title fon-S30 fon-W600 color-333 mar-B24">不同大小</text>
        <view class="demo-item back-white border-R12 pad-24 mar-B24">
          <text class="fon-S26 color-666">小、中、大三种尺寸，以及自定义尺寸</text>
        </view>
      </view>

      <view class="section mar-B48">
        <text class="section-title fon-S30 fon-W600 color-333 mar-B24">拖拽功能</text>
        <view class="demo-item back-white border-R12 pad-24 mar-B24">
          <text class="fon-S26 color-666">可拖拽的悬浮按钮，支持边界限制</text>
        </view>
      </view>

      <view class="section mar-B48">
        <text class="section-title fon-S30 fon-W600 color-333 mar-B24">显示控制</text>
        <view class="demo-item back-white border-R12 pad-24 mar-B24">
          <view class="f-between f-y-center">
            <text class="fon-S26 color-666">控制悬浮按钮的显示与隐藏</text>
            <wd-button size="small" :type="showButtons ? 'primary' : 'default'" @click="toggleButtons">
              {{ showButtons ? '隐藏按钮' : '显示按钮' }}
            </wd-button>
          </view>
        </view>
      </view>

      <view class="section">
        <text class="section-title fon-S30 fon-W600 color-333 mar-B24">操作记录</text>
        <view class="log-container back-white border-R12 pad-24">
          <view v-if="logs.length === 0" class="f-xy-center" style="height: 200rpx">
            <text class="fon-S26 color-999">点击悬浮按钮查看操作记录</text>
          </view>
          <view v-else>
            <view v-for="(log, index) in logs" :key="index" class="log-item f-between f-y-center pad-Y12 border-B-eee">
              <text class="fon-S26 color-333">{{ log.action }}</text>
              <text class="fon-S22 color-999">{{ log.time }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 悬浮按钮示例 -->
    <!-- 默认按钮 -->
    <FloatingButton :visible="showButtons" @click="handleDefaultClick" />

    <!-- 左上角 - 消息按钮 -->
    <FloatingButton :visible="showButtons" position="left-top" icon="message" background-color="#52c41a" size="small" @click="handleMessageClick" />

    <!-- 右上角 - 设置按钮 -->
    <FloatingButton :visible="showButtons" position="right-top" icon="setting" background-color="#ff4d4f" size="medium" @click="handleSettingClick" />

    <!-- 左下角 - 自定义内容 -->
    <FloatingButton :visible="showButtons" position="left-bottom" background-color="#722ed1" size="large" @click="handleCustomClick">
      <view class="f-xy-center">
        <text class="fon-S28 color-white fon-W600">?</text>
      </view>
    </FloatingButton>

    <!-- 可拖拽按钮 - 自定义尺寸 -->
    <FloatingButton
      :visible="showButtons"
      :draggable="true"
      :size="150"
      icon="drag"
      background-color="#fa8c16"
      :initial-position="{ x: 100, y: 200 }"
      :bounds="{ top: 100, right: 50, bottom: 200, left: 50 }"
      @click="handleDragClick"
      @position-change="handlePositionChange"
    />

    <!-- 可拖拽按钮 - 预设尺寸 -->
    <FloatingButton :visible="showButtons" :draggable="true" size="medium" icon="move" background-color="#13c2c2" :initial-position="{ x: 200, y: 300 }" :bounds="{ top: 100, right: 50, bottom: 200, left: 50 }" @click="handleMoveClick" />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import FloatingButton from './index.vue'

const logs = ref([])
const showButtons = ref(true)

function addLog(action) {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

  logs.value.unshift({
    action,
    time
  })

  // 最多保留10条记录
  if (logs.value.length > 10) {
    logs.value = logs.value.slice(0, 10)
  }
}

function handleDefaultClick() {
  addLog('点击了默认按钮（右下角）')
}

function handleMessageClick() {
  addLog('点击了消息按钮（左上角）')
}

function handleSettingClick() {
  addLog('点击了设置按钮（右上角）')
}

function handleCustomClick() {
  addLog('点击了帮助按钮（左下角）')
}

function handleDragClick() {
  addLog('点击了可拖拽按钮（橙色）')
}

function handleMoveClick() {
  addLog('点击了可拖拽按钮（青色）')
}

function handlePositionChange(position) {
  addLog(`拖拽按钮位置变化：x=${Math.round(position.x)}, y=${Math.round(position.y)}`)
}

function toggleButtons() {
  showButtons.value = !showButtons.value
  addLog(`${showButtons.value ? '显示' : '隐藏'}了所有悬浮按钮`)
}
</script>

<style lang="less" scoped>
.example-page {
  overflow-y: auto;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.section-title {
  display: block;
}

.demo-item {
  border: 2rpx solid #f0f0f0;
}

.log-container {
  border: 2rpx solid #f0f0f0;
  max-height: 400rpx;
  overflow-y: auto;
}

.log-item:last-child {
  border-bottom: none;
}
</style>
