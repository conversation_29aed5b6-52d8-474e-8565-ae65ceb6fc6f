const zoneColor = {
  梅林片区: '#1677ff',
  景田片区: 'red',
  香蜜片区: '#fa9600',
  福东南片区: '#0000FF',
  福东北片区: '#ffd447',
  莲花片区: '#bdfb9b',
  中心城片区: '#ffb4a6',
  福保片区: '#9f4fff',
  福民片区: '#00ffff',
  新洲片区: '#00ff00'
}
function transitionArr(category, rest) {
  const entries = Object.entries(category)
  const entriesArr = entries.flat(1)
  if (rest) entriesArr.push(rest)
  return entriesArr
}

// 基础默认配置
const basicsConfig = {
  // 初始化地图默认基础配置
  map: {
    accessToken: 'pk.eyJ1Ijoibm9ydGh2aSIsImEiOiJjbGVydjM1OXYwMnpkM3BxZGw5Ynlrbm13In0.queCazXHMgl8WHfZ1lF4xg',
    center: [114.05528061331722, 22.54140197444606, 0], //中心点
    zoom: 12, //默认缩放
    minZoom: 10, //最小缩放
    maxZoom: 22, //最大缩放
    projection: 'globe', //球形地图
    antialias: true, //抗锯齿, //球形地图
    style: {
      version: 8,
      sources: {
        // 天地图矢量
        'osm-tiles1': {
          type: 'raster',
          tiles: ['https://t4.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'],
          tileSize: 256,
          maxzoom: 18
        },
        'osm-tiles2': {
          type: 'raster',
          tiles: ['https://t4.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=c4422fec9d5e394411da10d3f1838c84'],
          tileSize: 256,
          maxzoom: 18
        },
        //地形图
        Polyline_FT: {
          type: 'vector',
          scheme: 'tms',
          tiles: ['https://www.szwgft.cn:8889/geoserver/gwc/service/tms/1.0.0/Code%3APolyline_FT@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf']
        },
        // 无缝区块
        address_source: {
          type: 'vector',
          scheme: 'tms',
          tiles: ['https://www.szwgft.cn:8889/geoserver/gwc/service/tms/1.0.0/Code%3AFT_ZONE@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf']
        },
        Comparison: {
          type: 'vector',
          scheme: 'tms',
          tiles: ['https://www.szwgft.cn/mapServer/geoserver/gwc/service/tms/1.0.0/Code%3AFT_2022_drain_away_water@EPSG%3A3857@pbf/{z}/{x}/{y}.pbf']
        },
        'dot-source': {
          type: 'geojson',
          data: { type: 'FeatureCollection', features: [] }
        },
        'oneself-source': {
          type: 'geojson',
          data: { type: 'FeatureCollection', features: [] }
        }
      },
      glyphs: 'mapbox://fonts/mapbox/{fontstack}/{range}.pbf',
      layers: [
        // 天地图底图
        {
          id: 'simple-tiles1',
          type: 'raster',
          source: 'osm-tiles1'
        },
        {
          id: 'simple-tiles2',
          type: 'raster',
          source: 'osm-tiles2'
        },
        {
          id: 'PolylineFT_line',
          type: 'line',
          minzoom: 16.5,
          'source-layer': 'Polyline_FT',
          source: 'Polyline_FT',
          paint: { 'line-width': 2, 'line-color': '#666' }
        },

        // 无缝区块
        {
          id: 'zone-fill',
          type: 'fill',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          paint: {
            'fill-opacity': 0.35,
            'fill-color': [
              'case',
              ['==', ['get', 'Type'], '市政路'],
              '#999',
              ['==', ['get', 'Type'], '其他'],
              '#888',
              [
                'match',
                ['get', 'ManagerNam'], // type 等于 小区，根据 ManagerNam 设置颜色
                ['福中水务所'],
                '#ee0000',
                ['福东水务所'],
                '#bdfb9b',
                ['梅林水务所'],
                '#1677ff',
                ['香蜜水务所'],
                '#fa9600',
                '#999' // 默认颜色，如果 ManagerNam 不匹配以上任何值
              ]
            ]
          }
        },
        {
          id: 'zone-line',
          type: 'line',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          paint: {
            'line-width': 1,
            'line-color': '#343434'
          }
        },
        {
          id: 'zone-line2',
          type: 'line',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          filter: ['==', ['get', 'Zone_Name'], ''],
          paint: {
            'line-width': 5,
            'line-color': '#07c160'
          }
        },
        {
          id: 'zone-symbol',
          type: 'symbol',
          'source-layer': 'FT_ZONE',
          source: 'address_source',
          minzoom: 14,
          layout: {
            'text-field': ['get', 'Zone_Name'],
            'text-anchor': 'center',
            'text-size': 10
          },
          paint: {
            'text-color': 'black',
            'text-halo-color': 'white',
            'text-halo-width': 1
          }
        },
        // 2022排水设施
        // 2004污水管
        {
          id: 'Comparison2004WS_line',
          type: 'line',
          'source-layer': 'Pipe_2004',
          source: 'Comparison',
          minzoom: 15,
          layout: { visibility: 'none' },
          paint: { 'line-width': 4, 'line-opacity': 1, 'line-color': '#199f33' }
        },
        // 2004污水井
        {
          id: 'Comparison2004Manhole_symbol',
          type: 'symbol',
          'source-layer': 'Manhole_2004',
          source: 'Comparison',
          minzoom: 15,
          layout: {
            visibility: 'none',
            'icon-image': '污水井',
            'icon-size': 0.015,
            'icon-rotate': -90,
            'icon-allow-overlap': true
          },
          paint: {
            'icon-opacity': 0.6 // 正确使用 paint 属性
          }
        },
        // 2022雨污管
        {
          id: 'tubeLayer_line',
          type: 'line',
          'source-layer': 'PS_PIPE',
          minzoom: 15,
          source: 'Comparison',
          layout: { visibility: 'none' },
          paint: {
            'line-color': ['case', ['==', ['get', 'SUBTYPE'], '雨水管'], '#0080ff', '#ff0000'],
            'line-width': 4,
            'line-opacity': 1
          }
        },

        // 2022排水渠
        {
          id: 'canalLayer_line',
          type: 'line',
          'source-layer': 'PS_CONDUIT',
          minzoom: 15,
          source: 'Comparison',
          layout: { visibility: 'none' },
          paint: {
            'line-color': ['case', ['==', ['get', 'SUBTYPE'], '雨水渠'], '#0080ff', '#ff0000'],
            'line-width': 4,
            'line-opacity': 1
          }
        },
        // 2022雨污井
        {
          id: 'well_symbol',
          'source-layer': 'WELL',
          source: 'Comparison',
          type: 'symbol',
          minzoom: 15,
          layout: {
            visibility: 'none',
            'icon-image': ['match', ['get', 'SUBTYPE'], ['YS'], '雨水井', '污水井'],
            'icon-rotate': -90,
            'icon-size': 0.015,
            'icon-allow-overlap': true
          }
        },
        // 2022雨水箅子
        {
          id: 'grate_symbol',
          'source-layer': 'RAINSTR',
          source: 'Comparison',
          minzoom: 15,
          type: 'symbol',
          layout: { visibility: 'none', 'icon-image': '箅子', 'icon-rotate': -90, 'icon-size': 0.015, 'icon-allow-overlap': true }
        },
        {
          id: 'point',
          type: 'symbol',
          source: 'dot-source',
          layout: { 'icon-image': ['get', 'icon'], 'icon-size': 0.3, 'icon-allow-overlap': false, 'icon-offset': [0, -50] }
        },
        {
          id: 'oneself',
          type: 'circle',
          source: 'oneself-source',
          paint: {
            'circle-radius': 4,
            'circle-color': '#46ba48',
            'circle-stroke-width': 2,
            'circle-stroke-color': '#0078d4'
          }
        }
      ]
    }
  }
}

export default basicsConfig
