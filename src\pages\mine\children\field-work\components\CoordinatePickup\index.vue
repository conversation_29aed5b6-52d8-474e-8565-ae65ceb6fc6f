<template>
  <div class="box">
    <view class="flex f-column all">
      <wd-search class="border-B-eee" v-model="text" @search="search" @cancel="() => (open = false)" />
      <div class="f-1"><MapGl ref="mapComponentRef" @handler="handler" :MarkerCenter /></div>
      <wd-action-sheet v-model="show" :actions="actions" @select="({ item }) => mapComponentRef.reach(item)" />
      <div class="absolute box-shadow f-xy-center pad-10 back-white border-R12 z-index-99" @click="handleOneself" style="bottom: 180rpx; right: 50rpx"><image style="width: 50rpx; height: 50rpx" src="/static/DW.png"></image></div>
    </view>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import MapGl from './MapGl/index.vue'
import uniUtil from '/src/utils/uniUtil'
import { getZoneDataLike } from '/src/services/model/map.js'
import { getZoneData } from '/src/services/model/map.js'

const open = defineModel()
const emit = defineEmits(['change'])
const props = defineProps({ allow: { type: Boolean, default: false }, MarkerCenter: Array })

const mapComponentRef = ref(null)
const text = ref('')
const show = ref(false)

const address = ref([])
const actions = computed(() => address.value.map((item) => ({ name: item.Zone_Name, Zone_Code: item.Zone_Code, Center_Point: item.Center_Point.split(',') })))

async function search() {
  try {
    const res = await getZoneDataLike(text.value)
    address.value = JSON.parse(res.data)
    if (!address.value.length) return uniUtil.showToast('未找到有效区块')
    show.value = true
  } catch (error) {
    uniUtil.showToast(error)
  }
}
async function handler(val) {
  try {
    const { data } = await getZoneData(val.code)
    const [res] = JSON.parse(data)
    if (!res) {
      open.value = false
      if (!props.allow) return uniUtil.showToast('无效地址信息，超出边界')
    }
    val.zone = res ?? null
    open.value = false
    emit('change', val)
  } catch (error) {
    uniUtil.showToast(error)
  }
}
defineExpose()
async function handleOneself() {
  const { latitude, longitude } = await uni.getLocation({ altitude: true })
  mapComponentRef.value.reach({ Center_Point: [longitude, latitude] })
}
</script>

<style lang="less" scoped>
.box {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>
