import { nodeRequest } from '../index'

// 模糊查询区块
export const getZoneDataLike = (ZoneName) => nodeRequest.get(`/api/api/Data/ZoneData_like?Zone_Name=${ZoneName}`)

//所在区块详情数据
export const getZoneData = (ZoneCode) => nodeRequest.get(`/api/api/Data/ZoneData?Zone_Code=${ZoneCode}`)

//查询问题列表
export const getRecordList = (station) => nodeRequest.get(`/api/api/OperationFeedback/station?station=${station}`)

export const getCheckVersion = (version) => nodeRequest.get(`/nodeServer/checkVersion?version=${version}`)
// 版本更新检测
export const checkVersion = (version) => nodeRequest.get(`/nodeServer/version/compare?version=${version}`)

export const coordinateTransformation = (data) => nodeRequest.post(`/nodeServer/coordinateTransformation`, data)
