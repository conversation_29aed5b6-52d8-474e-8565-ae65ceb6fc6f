<template>
  <div class="detail-page">
    <!-- 状态头部卡片 -->
    <div class="status-header mar-B24">
      <div class="status-card pad-24 back-white border-R16 box-shadow">
        <div class="status-main f-between">
          <div class="facility-info f-y-center">
            <div class="facility-icon f-xy-center mar-R16">
              <wd-icon :name="getFacilityIcon(detail.facility_Type)" size="20px" color="#fff"></wd-icon>
            </div>
            <div>
              <div class="facility-title fon-S32 fon-W700 color-primary">{{ detail.facility_Type || '设施维护' }}</div>
              <div class="task-type fon-S24 color-666 mar-T4">{{ detail.task_Type || '维护任务' }}</div>
            </div>
          </div>
          <div class="status-badge" :class="getStatusClass()">
            <div class="status-text fon-S24 fon-W600">{{ getStatusText() }}</div>
            <div class="status-icon">
              <wd-icon :name="getStatusIcon()" size="16px" color="#fff"></wd-icon>
            </div>
          </div>
        </div>

        <!-- 进度指示器 -->
        <div class="progress-indicator mar-T20">
          <div class="progress-steps f-between">
            <div class="step" :class="{ active: true }">
              <div class="step-icon f-xy-center">
                <wd-icon name="edit" size="12px" color="#fff"></wd-icon>
              </div>
              <div class="step-label fon-S20">创建</div>
            </div>
            <div class="step-line" :class="{ active: detail.path4?.length > 0 }"></div>
            <div class="step" :class="{ active: detail.path4?.length > 0 }">
              <div class="step-icon f-xy-center">
                <wd-icon name="check" size="12px" color="#fff"></wd-icon>
              </div>
              <div class="step-label fon-S20">完成</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <wd-form ref="form" :model="model">
      <!-- 基础信息 -->
      <div class="info-section mar-B24">
        <div class="section-header f-y-center mar-B16">
          <div class="section-icon f-xy-center mar-R12">
            <wd-icon name="info" size="16px" color="#4d63e0"></wd-icon>
          </div>
          <div class="section-title fon-S28 fon-W600">基础信息</div>
        </div>
        <div class="info-cards">
          <div class="info-card pad-20 back-white border-R16 box-shadow mar-B16">
            <div class="card-row f-between f-y-center">
              <div class="field-info f-y-center">
                <div class="field-icon f-xy-center mar-R12">
                  <wd-icon name="water" size="14px" color="#1890ff"></wd-icon>
                </div>
                <div class="field-label fon-S24 color-666">供排水设施类型</div>
              </div>
              <div class="field-value">
                <div class="value-tag facility-tag" :class="getFacilityTagClass(detail.facility_Type)">
                  {{ detail.facility_Type || '--' }}
                </div>
              </div>
            </div>
          </div>

          <div class="info-card pad-20 back-white border-R16 box-shadow mar-B16">
            <div class="card-row f-between f-y-center">
              <div class="field-info f-y-center">
                <div class="field-icon f-xy-center mar-R12">
                  <wd-icon name="office-building" size="14px" color="#52c41a"></wd-icon>
                </div>
                <div class="field-label fon-S24 color-666">上报组织</div>
              </div>
              <div class="field-value">
                <div class="value-text fon-S26 color-333 fon-W500">{{ detail.station || '--' }}</div>
              </div>
            </div>
          </div>

          <div class="info-card pad-20 back-white border-R16 box-shadow">
            <div class="card-row f-between f-y-center">
              <div class="field-info f-y-center">
                <div class="field-icon f-xy-center mar-R12">
                  <wd-icon name="tool" size="14px" color="#fa8c16"></wd-icon>
                </div>
                <div class="field-label fon-S24 color-666">任务类型</div>
              </div>
              <div class="field-value">
                <div class="value-tag task-tag" :class="getTaskTagClass(detail.task_Type)">
                  {{ detail.task_Type || '--' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 位置信息 -->
      <div class="info-section mar-B24">
        <div class="section-header f-y-center mar-B16">
          <div class="section-icon f-xy-center mar-R12">
            <wd-icon name="location" size="16px" color="#52c41a"></wd-icon>
          </div>
          <div class="section-title fon-S28 fon-W600">位置信息</div>
        </div>
        <div class="location-card pad-24 back-white border-R16 box-shadow">
          <!-- 地址信息 -->
          <div class="location-header f-y-center mar-B20">
            <div class="location-icon f-xy-center mar-R16">
              <wd-icon name="location" size="20px" color="#fff"></wd-icon>
            </div>
            <div>
              <div class="location-title fon-S28 fon-W600 color-333">{{ detail.subdistric || '位置信息' }}</div>
              <div class="location-subtitle fon-S22 color-666">详细坐标位置</div>
            </div>
          </div>

          <!-- 坐标信息 -->
          <div class="coordinates-grid">
            <div class="coordinate-item">
              <div class="coordinate-header f-y-center mar-B8">
                <div class="coordinate-icon f-xy-center mar-R8">
                  <wd-icon name="compass" size="12px" color="#1890ff"></wd-icon>
                </div>
                <div class="coordinate-label fon-S22 color-666">经度 (X)</div>
              </div>
              <div class="coordinate-value fon-S26 color-333 fon-W500">{{ detail.x || '--' }}</div>
            </div>

            <div class="coordinate-item">
              <div class="coordinate-header f-y-center mar-B8">
                <div class="coordinate-icon f-xy-center mar-R8">
                  <wd-icon name="compass" size="12px" color="#52c41a"></wd-icon>
                </div>
                <div class="coordinate-label fon-S22 color-666">纬度 (Y)</div>
              </div>
              <div class="coordinate-value fon-S26 color-333 fon-W500">{{ detail.y || '--' }}</div>
            </div>
          </div>

          <!-- 位置状态 -->
          <div class="location-status mar-T16">
            <div class="status-indicator f-y-center">
              <div class="status-dot" :class="detail.x && detail.y ? 'status-active' : 'status-inactive'"></div>
              <div class="status-text fon-S22 color-666">
                {{ detail.x && detail.y ? '位置信息已获取' : '暂无位置信息' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 现场信息 -->
      <div class="info-section mar-B24" v-if="detail.path1.length || detail.path2.length">
        <div class="section-header f-y-center mar-B16">
          <div class="section-icon f-xy-center mar-R12">
            <wd-icon name="camera" size="16px" color="#fa8c16"></wd-icon>
          </div>
          <div class="section-title fon-S28 fon-W600">现场信息</div>
        </div>
        <wd-cell-group custom-class="box-shadow border-R12 overflow-hidden" border>
          <wd-cell prop="path1" title="近景照片" title-width="120rpx" v-if="detail.path1.length">
            <div class="image-preview">
              <UploadImage cancel class="mr-t24" disabled :zoneCode="'OperationFeedbacks/' + detail.zone_Code" v-model="detail.path1" />
            </div>
          </wd-cell>
          <wd-cell prop="path2" title="远景照片" title-width="120rpx" v-if="detail.path2.length">
            <div class="image-preview">
              <UploadImage cancel class="mr-t24" disabled :zoneCode="'OperationFeedbacks/' + detail.zone_Code" v-model="detail.path2" />
            </div>
          </wd-cell>
        </wd-cell-group>
      </div>

      <!-- 问题信息 -->
      <div class="info-section mar-B24">
        <div class="section-header f-y-center mar-B16">
          <div class="section-icon f-xy-center mar-R12">
            <wd-icon name="warning" size="16px" color="#ff4d4f"></wd-icon>
          </div>
          <div class="section-title fon-S28 fon-W600">问题信息</div>
        </div>
        <div class="problem-card pad-24 back-white border-R16 box-shadow">
          <!-- 问题截图 -->
          <div class="image-section mar-B20" v-if="detail.path3.length">
            <div class="image-header f-y-center mar-B12">
              <div class="image-icon f-xy-center mar-R12">
                <wd-icon name="camera" size="14px" color="#ff4d4f"></wd-icon>
              </div>
              <div class="image-title fon-S24 fon-W600 color-333">问题截图</div>
            </div>
            <div class="image-gallery">
              <UploadImage cancel class="mr-t24" disabled :zoneCode="'OperationFeedbacks/' + detail.zone_Code" v-model="detail.path3" />
            </div>
          </div>

          <!-- 问题描述 -->
          <div class="description-section">
            <div class="description-header f-y-center mar-B12">
              <div class="description-icon f-xy-center mar-R12">
                <wd-icon name="edit" size="14px" color="#fa8c16"></wd-icon>
              </div>
              <div class="description-title fon-S24 fon-W600 color-333">问题描述</div>
            </div>
            <div class="description-content">
              <div class="description-text fon-S26 color-333 line-height-1-6" v-if="detail.remark1">
                {{ detail.remark1 }}
              </div>
              <div class="description-empty fon-S24 color-999" v-else>暂无问题描述</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 完成信息 -->
      <div class="info-section mar-B24" v-if="detail.path4.length || detail.remark2">
        <div class="section-header f-y-center mar-B16">
          <div class="section-icon f-xy-center mar-R12">
            <wd-icon name="check-circle" size="16px" color="#52c41a"></wd-icon>
          </div>
          <div class="section-title fon-S28 fon-W600">完成信息</div>
        </div>
        <div class="completion-card pad-24 back-white border-R16 box-shadow">
          <!-- 完成截图 -->
          <div class="image-section mar-B20" v-if="detail.path4.length">
            <div class="image-header f-y-center mar-B12">
              <div class="image-icon f-xy-center mar-R12">
                <wd-icon name="camera" size="14px" color="#52c41a"></wd-icon>
              </div>
              <div class="image-title fon-S24 fon-W600 color-333">完成截图</div>
            </div>
            <div class="image-gallery">
              <UploadImage cancel class="mr-t24" disabled :zoneCode="'OperationFeedbacks/' + detail.zone_Code" v-model="detail.path4" />
            </div>
          </div>

          <!-- 完成描述 -->
          <div class="description-section" v-if="detail.remark2">
            <div class="description-header f-y-center mar-B12">
              <div class="description-icon f-xy-center mar-R12">
                <wd-icon name="check" size="14px" color="#52c41a"></wd-icon>
              </div>
              <div class="description-title fon-S24 fon-W600 color-333">完成描述</div>
            </div>
            <div class="description-content">
              <div class="description-text fon-S26 color-333 line-height-1-6">
                {{ detail.remark2 }}
              </div>
            </div>
          </div>

          <!-- 完成状态 -->
          <div class="completion-status mar-T16">
            <div class="status-badge completion-badge">
              <wd-icon name="check-circle" size="16px" color="#52c41a" class="mar-R8"></wd-icon>
              <span class="status-text fon-S22 fon-W600" style="color: #52c41a">任务已完成</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作信息 -->
      <div class="info-section mar-B24">
        <div class="section-header f-y-center mar-B16">
          <div class="section-icon f-xy-center mar-R12">
            <wd-icon name="user" size="16px" color="#722ed1"></wd-icon>
          </div>
          <div class="section-title fon-S28 fon-W600">操作信息</div>
        </div>
        <div class="operation-cards">
          <div class="operation-card pad-20 back-white border-R12 box-shadow mar-B16">
            <div class="card-header f-y-center mar-B12">
              <div class="card-icon f-xy-center mar-R12">
                <wd-icon name="add-circle" size="14px" color="#52c41a"></wd-icon>
              </div>
              <div class="card-title fon-S24 fon-W600 color-333">创建信息</div>
            </div>
            <div class="card-content">
              <div class="info-row f-between mar-B8">
                <span class="info-label fon-S22 color-666">创建人员</span>
                <span class="info-value fon-S22 color-333 fon-W500">{{ detail.inputstaff || '--' }}</span>
              </div>
              <div class="info-row f-between">
                <span class="info-label fon-S22 color-666">创建时间</span>
                <span class="info-value fon-S22 color-333 fon-W500">{{ formatDateTime(detail.inputdate) }}</span>
              </div>
            </div>
          </div>

          <div class="operation-card pad-20 back-white border-R12 box-shadow" v-if="detail.veruserkey">
            <div class="card-header f-y-center mar-B12">
              <div class="card-icon f-xy-center mar-R12">
                <wd-icon name="edit" size="14px" color="#1890ff"></wd-icon>
              </div>
              <div class="card-title fon-S24 fon-W600 color-333">更新信息</div>
            </div>
            <div class="card-content">
              <div class="info-row f-between mar-B8">
                <span class="info-label fon-S22 color-666">更新人员</span>
                <span class="info-value fon-S22 color-333 fon-W500">{{ detail.veruserkey || '--' }}</span>
              </div>
              <div class="info-row f-between">
                <span class="info-label fon-S22 color-666">更新时间</span>
                <span class="info-value fon-S22 color-333 fon-W500">{{ formatDateTime(detail.verdate) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons mar-B30" v-if="!detail.path4.length && name === detail.inputstaff">
        <div class="button-group f-between">
          <!-- <wd-button type="error" class="action-btn delete-btn f-1 mar-R12" size="large" @click="handleDelete" block>
            <wd-icon name="delete" size="16px" class="mar-R8"></wd-icon>
            删除记录
          </wd-button> -->
          <wd-button type="primary" class="action-btn edit-btn f-1" size="large" @click="amendClick" block>
            <wd-icon name="edit" size="16px" class="mar-R8"></wd-icon>
            前往修改
          </wd-button>
        </div>
      </div>
      <div style="height: 80px"></div>
    </wd-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import UploadImage from '/src/components/UploadImage/index.vue'
import uniUtil from '/src/utils/uniUtil'
import { deleteOperationFeedbackApi, getOperationFeedbackDetailApi } from '/src/services/model/submit.js'
import { useMessage } from 'wot-design-uni'

const name = uniUtil.get('userInfo').name

const props = defineProps({ detail: { type: Object, default: () => ({}) } })

function amendClick() {
  uniUtil.navigateTo(`/src/pages/mine/children/record/update?id=${props.detail.id}`)
}

const message = useMessage()

function handleDelete() {
  message.confirm({ msg: '确定删除该条数据？', title: '删除' }).then(async () => {
    const { code } = await deleteOperationFeedbackApi(id)
    if (code != 200) return uniUtil.showToast('删除失败！')
    uniUtil.showToast('删除成功！')
    setTimeout(() => uniUtil.navigateBack(1), 2000)
  })
}

// 获取设施类型图标
function getFacilityIcon(facilityType) {
  const iconMap = {
    供水: 'water',
    雨水: 'cloud-rain',
    污水: 'water-drop',
    总阀: 'setting',
    总表: 'chart',
    管道: 'link',
    泵房: 'home',
    水表: 'chart-pie',
    阀门: 'setting',
    管网: 'link',
    设备: 'tool'
  }
  return iconMap[facilityType] || 'tool'
}

// 获取状态样式类
function getStatusClass() {
  return props.detail.path4?.length > 0 ? 'status-completed' : 'status-pending'
}

// 获取状态文本
function getStatusText() {
  return props.detail.path4?.length > 0 ? '已完成' : '处理中'
}

// 获取状态图标
function getStatusIcon() {
  return props.detail.path4?.length > 0 ? 'check-circle' : 'clock'
}

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '--'
  return dateTime.slice(0, 16).replace('T', ' ')
}

// 获取设施类型标签样式类
function getFacilityTagClass(facilityType) {
  const classMap = {
    供水: 'tag-supply',
    雨水: 'tag-rain',
    污水: 'tag-sewage'
  }
  return classMap[facilityType] || 'tag-default'
}

// 获取任务类型标签样式类
function getTaskTagClass(taskType) {
  const classMap = {
    维抢修: 'tag-repair',
    清疏: 'tag-clean',
    溯源: 'tag-trace',
    DN800专项: 'tag-special'
  }
  return classMap[taskType] || 'tag-default'
}
</script>

<style lang="less" scoped>
.detail-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 24rpx;
}

// 状态头部卡片
.status-header {
  .status-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    border-radius: 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4rpx;
      background: linear-gradient(90deg, #4d63e0 0%, #6366f1 100%);
    }
  }

  .facility-icon {
    width: 64rpx;
    height: 64rpx;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%);
    box-shadow: 0 4rpx 16rpx rgba(77, 99, 224, 0.3);
  }

  .facility-title {
    color: #4d63e0;
    line-height: 1.2;
  }

  .status-badge {
    padding: 12rpx 20rpx;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    gap: 8rpx;

    &.status-completed {
      background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
      border: 2rpx solid #b7eb8f;

      .status-text {
        color: #52c41a;
      }

      .status-icon {
        background: #52c41a;
        border-radius: 50%;
        width: 24rpx;
        height: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &.status-pending {
      background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
      border: 2rpx solid #ffe58f;

      .status-text {
        color: #faad14;
      }

      .status-icon {
        background: #faad14;
        border-radius: 50%;
        width: 24rpx;
        height: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// 进度指示器
.progress-indicator {
  .progress-steps {
    align-items: center;
  }

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;

    .step-icon {
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      background: #d9d9d9;
      transition: all 0.3s ease;

      &.active {
        background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
        box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
      }
    }

    .step-label {
      color: #999;
      transition: all 0.3s ease;
    }

    &.active .step-label {
      color: #52c41a;
      font-weight: 500;
    }
  }

  .step-line {
    flex: 1;
    height: 2rpx;
    background: #d9d9d9;
    margin: 0 16rpx;
    transition: all 0.3s ease;

    &.active {
      background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
    }
  }
}

// 信息区域
.info-section {
  .section-header {
    padding-left: 8rpx;
  }

  .section-icon {
    width: 32rpx;
    height: 32rpx;
    border-radius: 8rpx;
    background: rgba(77, 99, 224, 0.1);
  }

  .section-title {
    color: #333;
  }
}

// 信息卡片
.info-cards {
  .info-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    border-radius: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border-left: 4rpx solid #4d63e0;

    .card-row {
      align-items: center;
    }

    .field-info {
      flex: 1;
    }

    .field-icon {
      width: 32rpx;
      height: 32rpx;
      border-radius: 8rpx;
      background: rgba(77, 99, 224, 0.1);
    }

    .field-label {
      line-height: 1.3;
    }

    .field-value {
      text-align: right;
    }

    .value-text {
      line-height: 1.2;
    }

    .value-tag {
      padding: 6rpx 16rpx;
      border-radius: 16rpx;
      font-size: 22rpx;
      font-weight: 600;
      line-height: 1;

      &.facility-tag {
        &.tag-supply {
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          color: #1890ff;
          border: 1rpx solid #91d5ff;
        }

        &.tag-rain {
          background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
          color: #52c41a;
          border: 1rpx solid #b7eb8f;
        }

        &.tag-sewage {
          background: linear-gradient(135deg, #fff2e8 0%, #ffd591 100%);
          color: #fa8c16;
          border: 1rpx solid #ffcc02;
        }

        &.tag-default {
          background: linear-gradient(135deg, #f5f5f5 0%, #d9d9d9 100%);
          color: #666;
          border: 1rpx solid #d9d9d9;
        }
      }

      &.task-tag {
        &.tag-repair {
          background: linear-gradient(135deg, #fff0f6 0%, #ffd6e7 100%);
          color: #eb2f96;
          border: 1rpx solid #ffadd2;
        }

        &.tag-clean {
          background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
          color: #722ed1;
          border: 1rpx solid #d3adf7;
        }

        &.tag-trace {
          background: linear-gradient(135deg, #e6fffb 0%, #b5f5ec 100%);
          color: #13c2c2;
          border: 1rpx solid #87e8de;
        }

        &.tag-special {
          background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
          color: #fa541c;
          border: 1rpx solid #ffbb96;
        }

        &.tag-default {
          background: linear-gradient(135deg, #f5f5f5 0%, #d9d9d9 100%);
          color: #666;
          border: 1rpx solid #d9d9d9;
        }
      }
    }
  }
}

// 位置卡片
.location-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #52c41a;

  .location-icon {
    width: 48rpx;
    height: 48rpx;
    border-radius: 12rpx;
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
  }

  .location-title {
    line-height: 1.2;
  }

  .location-subtitle {
    line-height: 1.3;
    opacity: 0.8;
  }

  .coordinates-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20rpx;
    margin-bottom: 16rpx;
  }

  .coordinate-item {
    background: rgba(82, 196, 26, 0.04);
    padding: 16rpx;
    border-radius: 12rpx;
    border-left: 3rpx solid #52c41a;
  }

  .coordinate-header {
    align-items: center;
  }

  .coordinate-icon {
    width: 24rpx;
    height: 24rpx;
    border-radius: 6rpx;
    background: rgba(24, 144, 255, 0.1);
  }

  .coordinate-label {
    line-height: 1.2;
  }

  .coordinate-value {
    line-height: 1.2;
    word-break: break-all;
  }

  .location-status {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 16rpx;
  }

  .status-indicator {
    align-items: center;
  }

  .status-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    margin-right: 8rpx;

    &.status-active {
      background: #52c41a;
      box-shadow: 0 0 0 3rpx rgba(82, 196, 26, 0.2);
    }

    &.status-inactive {
      background: #d9d9d9;
    }
  }

  .status-text {
    line-height: 1.3;
  }
}

// 问题信息卡片
.problem-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #ff4d4f;

  .image-section {
    .image-header {
      align-items: center;
    }

    .image-icon {
      width: 32rpx;
      height: 32rpx;
      border-radius: 8rpx;
      background: rgba(255, 77, 79, 0.1);
    }

    .image-title {
      line-height: 1.2;
    }

    .image-gallery {
      border-radius: 12rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    }
  }

  .description-section {
    .description-header {
      align-items: center;
    }

    .description-icon {
      width: 32rpx;
      height: 32rpx;
      border-radius: 8rpx;
      background: rgba(250, 140, 22, 0.1);
    }

    .description-title {
      line-height: 1.2;
    }

    .description-content {
      background: rgba(255, 77, 79, 0.04);
      padding: 16rpx 20rpx;
      border-radius: 12rpx;
      border-left: 3rpx solid #ff4d4f;
    }

    .description-text {
      line-height: 1.6;
      word-break: break-word;
    }

    .description-empty {
      line-height: 1.4;
      font-style: italic;
    }
  }
}

// 完成信息卡片
.completion-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #52c41a;

  .image-section {
    .image-header {
      align-items: center;
    }

    .image-icon {
      width: 32rpx;
      height: 32rpx;
      border-radius: 8rpx;
      background: rgba(82, 196, 26, 0.1);
    }

    .image-title {
      line-height: 1.2;
    }

    .image-gallery {
      border-radius: 12rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    }
  }

  .description-section {
    .description-header {
      align-items: center;
    }

    .description-icon {
      width: 32rpx;
      height: 32rpx;
      border-radius: 8rpx;
      background: rgba(82, 196, 26, 0.1);
    }

    .description-title {
      line-height: 1.2;
    }

    .description-content {
      background: rgba(82, 196, 26, 0.04);
      padding: 16rpx 20rpx;
      border-radius: 12rpx;
      border-left: 3rpx solid #52c41a;
    }

    .description-text {
      line-height: 1.6;
      word-break: break-word;
    }
  }

  .completion-status {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 16rpx;
    text-align: center;
  }

  .completion-badge {
    display: inline-flex;
    align-items: center;
    padding: 8rpx 16rpx;
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    border: 1rpx solid #b7eb8f;
    border-radius: 20rpx;
  }

  .status-text {
    line-height: 1.2;
  }
}

// 通用样式
.line-height-1-6 {
  line-height: 1.6;
}

// 图片预览优化
.image-preview {
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

// 操作信息卡片
.operation-cards {
  .operation-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border-left: 4rpx solid transparent;

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
    }

    &:first-child {
      border-left-color: #52c41a;
    }

    &:last-child {
      border-left-color: #1890ff;
    }
  }

  .card-icon {
    width: 28rpx;
    height: 28rpx;
    border-radius: 6rpx;
    background: rgba(82, 196, 26, 0.1);
  }

  .info-row {
    align-items: center;
  }

  .info-label {
    opacity: 0.8;
  }

  .info-value {
    text-align: right;
  }
}

// 操作按钮
.action-buttons {
  .button-group {
    gap: 24rpx;
  }

  .action-btn {
    border-radius: 16rpx;
    height: 88rpx;
    font-size: 28rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    &.delete-btn {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      border: none;

      &:active {
        background: linear-gradient(135deg, #d9363e 0%, #ff4d4f 100%);
      }
    }

    &.edit-btn {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border: none;

      &:active {
        background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
      }
    }
  }
}

// 响应式优化
@media (max-width: 750rpx) {
  .status-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .facility-info {
    width: 100%;
  }

  .status-info {
    align-self: flex-end;
  }
}

// 动画效果
.info-section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .detail-page {
    background-color: #1a1a1a;
  }

  .status-card,
  .operation-card {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
  }

  .section-title,
  .facility-title,
  .card-title {
    color: #ffffff;
  }

  .info-label {
    color: #cccccc;
  }

  .info-value {
    color: #ffffff;
  }
}
</style>
