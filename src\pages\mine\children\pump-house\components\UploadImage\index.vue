<template>
  <view class="uploadImage">
    <!-- 画布图片压缩 -->
    <canvas class="canvas" canvas-id="mycanvas" id="mycanvas"> </canvas>
    <template v-for="(item, index) in images" :key="item">
      <view class="item" v-if="item">
        <wd-icon name="close-circle-filled" size="18px" v-if="!disabled" class="delete" @click="imageDelete(index)" />
        <image :src="item" class="img" mode="aspectFill" @click="previewImage(images, item)" />
      </view>
    </template>
    <view class="img upload" @click="uploadImage" v-if="maxlength > images?.length && !cancel">
      <image style="width: 25%" src="/static/camera.png" mode="widthFix" />
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import uniUtil from '/src/utils/uniUtil.js'
import { previewImage, chooseImage, getImageInfo, canvasToTempFilePath, uploadFile, handlerImage } from './index.js'

const props = defineProps({
  modelValue: { type: String, default: '' },
  maxlength: { type: Number, default: 2 }, // 上传数量
  url: { type: String },
  cancel: { type: Boolean, default: false },
  watermark: Object, // 添加水印
  disabled: { type: Boolean, default: false },
  quality: { type: Number, default: 0.3 } // quality 在0.2 略有瑕疵，在0.3 无明显差异瑕疵
})

const emit = defineEmits(['update:modelValue'])
const images = computed(() => (props.modelValue ?? '').split(',').filter((i) => !!i))

// 图片上传
async function uploadImage() {
  try {
    if (props.disabled) return uniUtil.showToast('上传已禁用')
    const { tempFilePaths } = await chooseImage()
    uni.showLoading({ mask: true, title: '上传中...' })
    const { width, height, path } = await getImageInfo(tempFilePaths[0])
    const ctx = uni.createCanvasContext('mycanvas')
    ctx.drawImage(path, 0, 0, width, height)
    if (props.watermark) await stampImage(ctx, props.watermark, height)
    ctx.draw()
    const res = await canvasToTempFilePath(width, height, 'mycanvas', props.quality)
    const { data } = await uploadFile(res.tempFilePath, props.url)
    images.value.push(data)
    emit('update:modelValue', images.value.join(','))
    uni.hideLoading()
  } catch (error) {
    console.log(error)
    uni.hideLoading()
    uniUtil.showToast('上传失败')
  }
}

// 添加水印
async function stampImage(ctx, watermarkData, height) {
  ctx.setFillStyle('#fff')
  ctx.setFontSize(30)
  ctx.setTextAlign('left')
  const values = Object.entries(watermarkData)
  values.forEach((item, index) => ctx.fillText(`${item[0]}:${item[1]}`, 40, height - 200 + index * 40))
}

// 图片删除
function imageDelete(index) {
  images.value.splice(index, 1)
  emit('update:modelValue', images.value.join(','))
}
</script>

<style lang="less" scoped>
.uploadImage {
  display: flex;
  padding: 12rpx;

  .upload {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #eee !important;
  }

  .item {
    position: relative;
    margin-right: 12rpx;

    .delete {
      position: absolute;
      top: -10rpx;
      right: -5rpx;
      z-index: 10;
    }
  }

  .img {
    width: 120rpx;
    height: 120rpx;
    border: 2rpx solid #999;
    border-radius: 12rpx;
    background-color: #eee;
  }

  .canvas {
    width: 2000px;
    height: 2000px;
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
}
</style>
