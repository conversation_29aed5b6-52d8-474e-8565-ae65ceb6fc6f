import { nodeRequest } from '../index'

// 二供列表
export const getSecondaryWaterProgressList = () => nodeRequest.get(`/api/api/SecondaryWaterProgress`, null, true)
// 详情
export const getSecondaryWaterProgressDetail = (id) => nodeRequest.get(`/api/api/SecondaryWaterProgress/id?id=${id}`)
// 新增
export const getSecondaryWaterProgressCreate = (data) => nodeRequest.post(`/api/api/SecondaryWaterProgress`, data)
// 修改
export const getSecondaryWaterProgressUpdate = (id, data) => nodeRequest.put(`/api/api/SecondaryWaterProgress?id=${id}`, data)

// 模糊搜索泵房
export const getPumpHouseNameLikeApi = (PumpHouseName) => nodeRequest.get(`/api/api/SecondaryWaterProgress/PumpHouseName_like?PumpHouseName=${PumpHouseName}`)
export const getZoneCodeLikeApi = (code) => nodeRequest.get(`/api/api/SecondaryWaterProgress/Zone_Code?Zone_Code=${code}`)

export const getPumpHouseFileList = (id) => nodeRequest.get(`/nodeServer/upload/pump_house/${id}`)
