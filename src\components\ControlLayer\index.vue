<template>
  <div class="control-layer">
    <div @click="openControlLayer">
      <slot name="icon">
        <div class="icon back-white box-shadow">
          <wd-icon name="layers" size="22px" />
        </div>
      </slot>
    </div>

    <wd-popup v-model="open" position="left" custom-style="min-width: 40%;">
      <div class="popup_box">
        <div class="mar-B24 fon-S30 fon-W600">图层控制</div>
        <template v-for="item in layerList" :key="item.layterId">
          <wd-checkbox shape="square" custom-class="checkbox_item" @change="(e) => handleLayterChange(e, item)" v-model="item.checked">{{ item.name }}</wd-checkbox>
        </template>
      </div>
    </wd-popup>
  </div>
</template>

<script setup>
import { ref } from 'vue'
const props = defineProps({ layerList: { type: Array, default: [] } })
const emit = defineEmits(['change'])

const open = ref(false)

function openControlLayer() {
  open.value = true
}

function handleLayterChange(e, item) {
  emit('change', { ...item, checked: e.value })
}
</script>

<style lang="less" scoped>
.control-layer {
  position: fixed;
  .icon {
    padding: 10rpx;
    border-radius: 12rpx;
  }
  .popup_box {
    padding: 120rpx 24rpx;
  }
}
.checkbox_item {
  margin-bottom: 8rpx !important;
}
</style>
