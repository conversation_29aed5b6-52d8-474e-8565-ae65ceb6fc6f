<template>
  <view class="create-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <view class="header-icon">
          <wd-icon name="add-circle" size="48rpx" color="#ffffff" />
        </view>
        <view class="header-text">
          <text class="header-title">新增记录</text>
          <text class="header-subtitle">创建新的维护记录</text>
        </view>
      </view>
    </view>

    <wd-form ref="form" :model="model" :rules="rules" class="form-container">
      <!-- 基础信息 -->
      <view class="form-section basic-info">
        <view class="section-header">
          <view class="section-icon basic-icon">
            <wd-icon name="setting" size="32rpx" color="#ffffff" />
          </view>
          <view class="section-title">
            <text class="title-text">基础信息</text>
            <text class="title-subtitle">设施类型和任务信息</text>
          </view>
        </view>
        <view class="section-content">
          <wd-picker required label="供排水设施类型" placeholder="选择类型" label-width="240rpx" prop="facility_Type" v-model="model.facility_Type" :columns="ProgressStatuslist" />
          <wd-input required disabled label="上报组织" label-width="240rpx" prop="station" suffix-icon="warn-bold" clearable v-model="model.station" placeholder="请输入埋深" />
          <wd-picker required label="任务类型" placeholder="选择类型" label-width="240rpx" prop="task_Type" v-model="model.task_Type" :columns="ProgressStatuslist2" />
        </view>
      </view>

      <!-- 位置信息 -->
      <view class="form-section location-info">
        <view class="section-header">
          <view class="section-icon location-icon">
            <wd-icon name="location" size="32rpx" color="#ffffff" />
          </view>
          <view class="section-title">
            <text class="title-text">位置信息</text>
            <text class="title-subtitle">地理位置和坐标信息</text>
          </view>
        </view>
        <view class="section-content">
          <view class="location-action">
            <wd-cell title="获取位置信息" required title-width="200rpx">
              <wd-button @click="open = true" type="success" size="small" class="location-btn">
                <wd-icon name="location" size="24rpx" />
                <text style="margin-left: 8rpx">获取位置</text>
              </wd-button>
            </wd-cell>
          </view>
          <wd-input label="所属街道" required label-width="240rpx" prop="subdistric" suffix-icon="warn-bold" clearable v-model="model.subdistric" placeholder="请输入区域名称" />
          <wd-input label="X坐标" label-width="240rpx" disabled show-word-limit prop="x" required suffix-icon="warn-bold" clearable v-model="model.x" placeholder="请获取位置信息" />
          <wd-input label="Y坐标" label-width="240rpx" disabled show-word-limit prop="y" required suffix-icon="warn-bold" clearable v-model="model.y" placeholder="请获取位置信息" />
        </view>
      </view>

      <!-- 现场信息 -->
      <view class="form-section site-info">
        <view class="section-header">
          <view class="section-icon site-icon">
            <wd-icon name="camera" size="32rpx" color="#ffffff" />
          </view>
          <view class="section-title">
            <text class="title-text">现场信息</text>
            <text class="title-subtitle">现场照片记录</text>
          </view>
        </view>
        <view class="section-content">
          <view class="upload-section">
            <wd-cell prop="path1" title="近景照片" title-width="150rpx">
              <UploadeImg :imageBaseUrl :maxlength="1" :handleImg="({ data }) => data" :url="`${ServerBaseUrl}/api/UpWrite/upload?Zone_Code=${'OperationFeedbacks/' + model.zone_Code}`" @success="handleSuccess" v-model="model.path1" />
            </wd-cell>
            <wd-cell prop="path2" title="远景照片" title-width="150rpx">
              <UploadeImg :imageBaseUrl :maxlength="1" :handleImg="({ data }) => data" :url="`${ServerBaseUrl}/api/UpWrite/upload?Zone_Code=${'OperationFeedbacks/' + model.zone_Code}`" @success="handleSuccess" v-model="model.path2" />
            </wd-cell>
          </view>
        </view>
      </view>

      <!-- 问题信息 -->
      <view class="form-section problem-info">
        <view class="section-header">
          <view class="section-icon problem-icon">
            <wd-icon name="warning" size="32rpx" color="#ffffff" />
          </view>
          <view class="section-title">
            <text class="title-text">问题信息</text>
            <text class="title-subtitle">问题描述和截图</text>
          </view>
        </view>
        <view class="section-content">
          <view class="upload-section">
            <wd-cell prop="path3" title="问题截图" required title-width="150rpx">
              <UploadeImg :imageBaseUrl :maxlength="1" :handleImg="({ data }) => data" :url="`${ServerBaseUrl}/api/UpWrite/upload?Zone_Code=${'OperationFeedbacks/' + model.zone_Code}`" @success="handleSuccess" v-model="model.path3" />
            </wd-cell>
          </view>
          <view class="textarea-section">
            <wd-textarea required label="修改描述" prop="remark1" auto-height label-width="100px" type="textarea" v-model="model.remark1" :maxlength="200" show-word-limit placeholder="请输入问题描述" clearable />
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <wd-button type="primary" class="submit-btn" size="large" @click="handleSubmit" block>
          <wd-icon name="check-circle" size="32rpx" />
          <text style="margin-left: 12rpx">提交新增</text>
        </wd-button>
      </view>
    </wd-form>

    <CoordinatePickup v-if="open" v-model="open" @change="change" />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import UploadeImg from '/src/components/UploadeImg/index.vue'
import { imageBaseUrl, ServerBaseUrl } from '/src/config/index.json'

import uniUtil from '/src/utils/uniUtil'
import { createRecordApi } from '/src/services/model/submit.js'

import { getCurrentDateTime } from '/src/utils'
import CoordinatePickup from './components/CoordinatePickup/index.vue'

onLoad((options) => {
  const { lng, lat } = JSON.parse(options.lngLat)
  model.value.zone_Code = options.code
  model.value.subdistric = options.Subdistric
  model.value.x = lng + ''
  model.value.y = lat + ''
})
onShow(() => (model.value.station = uniUtil.get('userInfo')?.station) ?? '未知')

const open = ref(false)
const model = ref({
  facility_Type: null, //供排水设施类型
  zone_Code: null,
  station: null, //上报组织
  subdistric: null, //所属街道
  task_Type: null, //任务类型
  path1: '', //近景照片：
  path2: '', //远景照片：
  path3: '', //GIS截图（问题处）
  path4: '', //
  remark1: null, //修改描述
  remark2: '', //
  x: null,
  y: null,
  inputstaff: null, //创建人
  inputdate: null, //创建时间
  veruserkey: null, //修改人
  verdate: null //修改时间
})

const ProgressStatuslist = ref([
  { value: '供水', label: '供水' },
  { value: '雨水', label: '雨水' },
  { value: '污水', label: '污水' }
])
const ProgressStatuslist2 = ref([
  { value: '维抢修', label: '维抢修' },
  { value: '清疏', label: '清疏' },
  { value: '溯源', label: '溯源' },
  { value: 'DN800专项', label: 'DN800专项' }
])

function change(e) {
  model.value.x = e.x + ''
  model.value.y = e.y + ''
  model.value.subdistric = e.feature.Subdistric ?? '超出边界'
  model.value.zone_Code = e.code ?? '超出边界'
}

function handleSuccess(data) {
  console.log('上传成功:', data)
}

const form = ref(null)
const rules = {
  facility_Type: [{ required: true, message: '请选择供排水设施类型', validator: (value) => (value ? Promise.resolve() : Promise.reject('请选择供排水设施类型')) }],
  task_Type: [{ required: true, message: '请选择任务类型', validator: (value) => (value ? Promise.resolve() : Promise.reject('请选择任务类型')) }],
  subdistric: [{ required: true, message: '请获取位置信息', validator: (value) => (value ? Promise.resolve() : Promise.reject('请获取位置信息')) }],
  path3: [{ required: true, message: '请上传问题点截图', validator: (value) => (value ? Promise.resolve() : Promise.reject('请上传问题点截图')) }],
  remark1: [{ required: true, message: '请填写修改描述', validator: (value) => (value ? Promise.resolve() : Promise.reject('请填写修改描述')) }],
  x: [{ required: true, message: '必须获取位置信息', validator: (value) => (value ? Promise.resolve() : Promise.reject('必须获取位置信息')) }],
  y: [{ required: true, message: '必须获取位置信息', validator: (value) => (value ? Promise.resolve() : Promise.reject('必须获取位置信息')) }]
}
function handleSubmit() {
  const userInfo = uniUtil.get('userInfo')
  const time = getCurrentDateTime()

  model.value.inputstaff = userInfo.name
  model.value.veruserkey = userInfo.name
  model.value.inputdate = time
  model.value.verdate = time

  form.value
    .validate()
    .then(async ({ valid, errors }) => {
      if (valid) {
        try {
          const res = await createRecordApi(model.value)
          if (res.code === 200) {
            uniUtil.showToast('提交成功')
            setTimeout(() => uniUtil.redirectTo(`/src/pages/mine/children/record/detail?id=${res.data.operationFeedback.id}`), 2000)
          }
        } catch (error) {
          uniUtil.showToast('提交失败-' + error)
        }
      } else {
        uniUtil.showToast(errors[0].message)
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}
</script>

<style lang="scss" scoped>
// 页面整体样式
.create-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 40rpx;
}

// 页面头部
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 32rpx 40rpx;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }
}

.header-content {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 2;
}

.header-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-text {
  flex: 1;
}

.header-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.header-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

// 表单容器
.form-container {
  margin-top: -20rpx;
  position: relative;
  z-index: 1;
}

// 表单分组
.form-section {
  margin: 0 24rpx 32rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.6s ease-out;

  &:nth-child(2) {
    animation-delay: 0.1s;
  }
  &:nth-child(3) {
    animation-delay: 0.2s;
  }
  &:nth-child(4) {
    animation-delay: 0.3s;
  }
  &:nth-child(5) {
    animation-delay: 0.4s;
  }
}

// 分组头部
.section-header {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.section-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.basic-icon {
  background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%);
}

.location-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.site-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.problem-icon {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.section-title {
  flex: 1;
}

.title-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.title-subtitle {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

// 分组内容
.section-content {
  padding: 0;
}

// 位置操作按钮
.location-action {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-bottom: 1px solid rgba(16, 185, 129, 0.1);
}

.location-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  border: none !important;
  border-radius: 12rpx !important;
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 140rpx !important;
  height: 56rpx !important;

  &:active {
    transform: scale(0.95);
  }
}

// 上传区域
.upload-section {
  background: #fafafa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.textarea-section {
  padding: 0;
}

// 提交区域
.submit-section {
  margin: 40rpx 24rpx 0;
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 16rpx !important;
  height: 88rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4) !important;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .page-header {
    padding: 50rpx 24rpx 30rpx;
  }

  .header-icon {
    width: 64rpx;
    height: 64rpx;
    margin-right: 20rpx;
  }

  .header-title {
    font-size: 32rpx;
  }

  .header-subtitle {
    font-size: 22rpx;
  }

  .form-section {
    margin: 0 16rpx 24rpx;
  }

  .section-header {
    padding: 20rpx 24rpx;
  }

  .section-icon {
    width: 56rpx;
    height: 56rpx;
    margin-right: 16rpx;
  }

  .title-text {
    font-size: 28rpx;
  }

  .title-subtitle {
    font-size: 22rpx;
  }

  .submit-section {
    margin: 32rpx 16rpx 0;
  }

  .submit-btn {
    height: 80rpx !important;
    font-size: 28rpx !important;
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .create-page {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }

  .form-section {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .section-header {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .title-text {
    color: #f9fafb;
  }

  .title-subtitle {
    color: #d1d5db;
  }

  .upload-section {
    background: #374151;
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .location-action {
    background: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
    border-bottom-color: rgba(16, 185, 129, 0.2);
  }
}

// 组件样式覆盖
:deep(.wd-cell-group) {
  border-radius: 0 !important;
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
}

:deep(.wd-cell) {
  background: #ffffff !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;

  &:last-child {
    border-bottom: none !important;
  }
}

:deep(.wd-input) {
  background: #ffffff !important;
}

:deep(.wd-textarea) {
  background: #ffffff !important;
}

:deep(.wd-picker) {
  background: #ffffff !important;
}
</style>
