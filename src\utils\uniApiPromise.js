export function getImageInfo(url) {
  return new Promise((resovle, reject) => {
    uni.getImageInfo({
      src: url,
      success: (res) => resovle(res),
      fail: (err) => reject(err)
    })
  })
}


export function chooseImage(config) {
  return new Promise((resovle, reject) => {
    uni.chooseImage({
      ...config,
      success: (res) => resovle(res),
      fail: (err) => reject(err)
    })
  })
}


export function compressImage(url, config) {
  return new Promise((resovle, reject) => {
    uni.compressImage({
      ...config,
      src: url,
      quality: 20,
      success: res => resovle(res)
    })
  })
}