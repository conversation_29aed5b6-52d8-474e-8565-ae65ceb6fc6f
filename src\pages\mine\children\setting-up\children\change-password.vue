<template>
  <div class="change-password-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content f-xy-center f-column">
        <div class="security-icon f-xy-center mar-B20">
          <wd-icon name="lock-on" size="32px" color="#fff"></wd-icon>
        </div>
        <div class="page-title fon-S40 fon-W700 color-333 mar-B8">修改密码</div>
        <div class="page-subtitle fon-S24 color-666">为了您的账户安全，请设置强密码</div>
      </div>
    </div>

    <!-- 表单区域 -->
    <div class="form-container">
      <!-- 当前密码 -->
      <div class="input-group mar-B32">
        <div class="input-label fon-S26 fon-W500 color-333 mar-B12">
          <wd-icon name="lock-on" size="16px" color="#4d63e0" class="mar-R8"></wd-icon>
          当前密码
        </div>
        <div class="input-wrapper" :class="{ 'input-error': errors.oldPassword }">
          <wd-input custom-class="custom-input" prefix-icon="lock-on" no-border v-model="T.password" placeholder="请输入当前密码" clearable show-password @input="validateOldPassword" />
        </div>
        <div class="error-message fon-S22 color-error" v-if="errors.oldPassword">{{ errors.oldPassword }}</div>
      </div>

      <!-- 新密码 -->
      <div class="input-group mar-B32">
        <div class="input-label fon-S26 fon-W500 color-333 mar-B12">
          <wd-icon name="key" size="16px" color="#52c41a" class="mar-R8"></wd-icon>
          新密码
        </div>
        <div class="input-wrapper" :class="{ 'input-error': errors.newPassword }">
          <wd-input custom-class="custom-input" prefix-icon="key" no-border v-model="T.password_new" placeholder="请输入新密码" clearable show-password @input="validateNewPassword" />
        </div>
        <!-- 密码强度指示器 -->
        <div class="password-strength mar-T12" v-if="T.password_new">
          <div class="strength-label fon-S22 color-666 mar-B8">密码强度：</div>
          <div class="strength-bar">
            <div class="strength-item" :class="{ active: passwordStrength >= 1, weak: passwordStrength === 1 }"></div>
            <div class="strength-item" :class="{ active: passwordStrength >= 2, medium: passwordStrength === 2 }"></div>
            <div class="strength-item" :class="{ active: passwordStrength >= 3, strong: passwordStrength === 3 }"></div>
          </div>
          <div class="strength-text fon-S22" :class="getStrengthClass()">{{ getStrengthText() }}</div>
        </div>
        <div class="error-message fon-S22 color-error" v-if="errors.newPassword">{{ errors.newPassword }}</div>
      </div>

      <!-- 确认密码 -->
      <div class="input-group mar-B40">
        <div class="input-label fon-S26 fon-W500 color-333 mar-B12">
          <wd-icon name="check-circle" size="16px" color="#fa8c16" class="mar-R8"></wd-icon>
          确认密码
        </div>
        <div class="input-wrapper" :class="{ 'input-error': errors.confirmPassword, 'input-success': T.password_new && newPassword && T.password_new === newPassword }">
          <wd-input custom-class="custom-input" prefix-icon="check-circle" no-border v-model="newPassword" placeholder="请再次输入新密码" clearable show-password @input="validateConfirmPassword" />
          <div class="input-status-icon" v-if="T.password_new && newPassword">
            <wd-icon :name="T.password_new === newPassword ? 'check' : 'close'" :color="T.password_new === newPassword ? '#52c41a' : '#ff4d4f'" size="16px"></wd-icon>
          </div>
        </div>
        <div class="error-message fon-S22 color-error" v-if="errors.confirmPassword">{{ errors.confirmPassword }}</div>
        <div class="success-message fon-S22 color-success" v-if="T.password_new && newPassword && T.password_new === newPassword">密码匹配</div>
      </div>
    </div>

    <!-- 安全提示 -->
    <div class="security-tips mar-B40">
      <div class="tips-header f-y-center mar-B16">
        <wd-icon name="info" size="16px" color="#1890ff" class="mar-R8"></wd-icon>
        <div class="tips-title fon-S26 fon-W500 color-333">安全提示</div>
      </div>
      <div class="tips-content">
        <div class="tip-item f-y-center mar-B12">
          <div class="tip-dot"></div>
          <div class="tip-text fon-S22 color-666">密码长度至少8位，包含字母和数字</div>
        </div>
        <div class="tip-item f-y-center mar-B12">
          <div class="tip-dot"></div>
          <div class="tip-text fon-S22 color-666">建议包含大小写字母、数字和特殊字符</div>
        </div>
        <div class="tip-item f-y-center">
          <div class="tip-dot"></div>
          <div class="tip-text fon-S22 color-666">定期更换密码，确保账户安全</div>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <wd-button :disabled="!canSubmit || isSubmitting" :loading="isSubmitting" custom-class="submit-button" size="large" @click="handleSubmit" type="primary" block>
        {{ isSubmitting ? '修改中...' : '确认修改' }}
      </wd-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { updatePassword } from '/src/services/model/login'
import uniUtil from '/src/utils/uniUtil'

// 表单数据
const T = reactive({ username: null, password: null, password_new: null })
const newPassword = ref(null)
const isSubmitting = ref(false)

// 错误信息
const errors = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码强度
const passwordStrength = computed(() => {
  if (!T.password_new) return 0

  let strength = 0
  const password = T.password_new

  // 长度检查
  if (password.length >= 8) strength++

  // 复杂度检查
  if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++
  if (/\d/.test(password) && /[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++

  return Math.min(strength, 3)
})

// 是否可以提交
const canSubmit = computed(() => {
  return T.password && T.password_new && newPassword.value && T.password_new === newPassword.value && passwordStrength.value >= 1 && !errors.oldPassword && !errors.newPassword && !errors.confirmPassword
})

// 验证函数
function validateOldPassword() {
  if (!T.password) {
    errors.oldPassword = '请输入当前密码'
  } else {
    errors.oldPassword = ''
  }
}

function validateNewPassword() {
  if (!T.password_new) {
    errors.newPassword = '请输入新密码'
  } else if (T.password_new.length < 8) {
    errors.newPassword = '密码长度至少8位'
  } else if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(T.password_new)) {
    errors.newPassword = '密码必须包含字母和数字'
  } else if (T.password === T.password_new) {
    errors.newPassword = '新密码不能与当前密码相同'
  } else {
    errors.newPassword = ''
  }

  // 重新验证确认密码
  if (newPassword.value) {
    validateConfirmPassword()
  }
}

function validateConfirmPassword() {
  if (!newPassword.value) {
    errors.confirmPassword = '请确认新密码'
  } else if (T.password_new !== newPassword.value) {
    errors.confirmPassword = '两次输入的密码不一致'
  } else {
    errors.confirmPassword = ''
  }
}

// 获取密码强度样式类
function getStrengthClass() {
  switch (passwordStrength.value) {
    case 1:
      return 'strength-weak'
    case 2:
      return 'strength-medium'
    case 3:
      return 'strength-strong'
    default:
      return ''
  }
}

// 获取密码强度文本
function getStrengthText() {
  switch (passwordStrength.value) {
    case 1:
      return '弱'
    case 2:
      return '中等'
    case 3:
      return '强'
    default:
      return ''
  }
}
// 提交处理
async function handleSubmit() {
  // 验证所有字段
  validateOldPassword()
  validateNewPassword()
  validateConfirmPassword()

  if (!canSubmit.value) {
    return uniUtil.showToast('请检查输入信息')
  }

  isSubmitting.value = true

  try {
    const userInfo = uniUtil.get('userInfo')
    const token = userInfo.token
    T.username = userInfo.Username
    const res = await updatePassword(token, T)
    if (res == '密码更新成功') {
      uniUtil.clear()
      uniUtil.showToast('密码修改成功')
      setTimeout(() => {
        uniUtil.reLaunch('/src/pages/login/index')
      }, 1500)
    }
  } catch (error) {
    console.error('修改密码失败:', error)
    uniUtil.showToast('修改失败，请重试   ' + error.data)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style lang="less" scoped>
.change-password-page {
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0 32rpx 40rpx;
}

// 页面头部
.page-header {
  padding: 80rpx 0 60rpx;
  text-align: center;

  .security-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%);
    box-shadow: 0 8rpx 32rpx rgba(77, 99, 224, 0.3);
  }

  .page-title {
    color: #333;
    line-height: 1.2;
  }

  .page-subtitle {
    line-height: 1.4;
    opacity: 0.8;
  }
}

// 表单容器
.form-container {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 32rpx;
}

// 输入组
.input-group {
  .input-label {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .input-wrapper {
    position: relative;
    border-radius: 16rpx;
    background: #f8f9fa;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #4d63e0;
      background: #fff;
      box-shadow: 0 0 0 4rpx rgba(77, 99, 224, 0.1);
    }

    &.input-error {
      border-color: #ff4d4f;
      background: #fff2f0;
    }

    &.input-success {
      border-color: #52c41a;
      background: #f6ffed;
    }
  }

  .input-status-icon {
    position: absolute;
    right: 16rpx;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }

  .error-message {
    margin-top: 8rpx;
    line-height: 1.4;
  }

  .success-message {
    margin-top: 8rpx;
    line-height: 1.4;
  }
}

// 自定义输入框样式
:deep(.custom-input) {
  background: transparent !important;
  border: none !important;
  padding: 20rpx 16rpx !important;

  .wd-input__inner {
    background: transparent !important;
    border: none !important;
    font-size: 28rpx !important;
  }
}

// 密码强度指示器
.password-strength {
  .strength-bar {
    display: flex;
    gap: 8rpx;
    margin-bottom: 8rpx;
  }

  .strength-item {
    flex: 1;
    height: 6rpx;
    border-radius: 3rpx;
    background: #e8e8e8;
    transition: all 0.3s ease;

    &.active.weak {
      background: linear-gradient(90deg, #ff4d4f 0%, #ff7875 100%);
    }

    &.active.medium {
      background: linear-gradient(90deg, #faad14 0%, #ffc53d 100%);
    }

    &.active.strong {
      background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
    }
  }

  .strength-text {
    text-align: right;

    &.strength-weak {
      color: #ff4d4f;
    }

    &.strength-medium {
      color: #faad14;
    }

    &.strength-strong {
      color: #52c41a;
    }
  }
}

// 安全提示
.security-tips {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

  .tips-header {
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .tips-content {
    padding-top: 16rpx;
  }

  .tip-item {
    align-items: flex-start;
  }

  .tip-dot {
    width: 8rpx;
    height: 8rpx;
    border-radius: 50%;
    background: #1890ff;
    margin-right: 12rpx;
    margin-top: 8rpx;
    flex-shrink: 0;
  }

  .tip-text {
    line-height: 1.5;
  }
}

// 提交按钮
.submit-section {
  padding: 0 32rpx;
  margin-top: 64rpx;
}

:deep(.submit-button) {
  height: 88rpx !important;
  border-radius: 16rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%) !important;
  border: none !important;
  box-shadow: 0 8rpx 24rpx rgba(77, 99, 224, 0.3) !important;
  transition: all 0.3s ease !important;

  &:active {
    transform: scale(0.98) !important;
    box-shadow: 0 4rpx 16rpx rgba(77, 99, 224, 0.4) !important;
  }

  &:disabled {
    background: #d9d9d9 !important;
    box-shadow: none !important;
    transform: none !important;
  }
}

// 颜色类
.color-error {
  color: #ff4d4f;
}

.color-success {
  color: #52c41a;
}

.color-primary {
  color: #4d63e0;
}

.color-333 {
  color: #333;
}

.color-666 {
  color: #666;
}

// 动画效果
.input-group {
  animation: fadeInUp 0.6s ease-out;
}

.security-tips {
  animation: fadeInUp 0.8s ease-out;
}

.submit-section {
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .page-header {
    padding: 60rpx 0 40rpx;
  }

  .form-container {
    padding: 32rpx 24rpx;
  }

  .security-tips {
    padding: 20rpx;
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .change-password-page {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .form-container,
  .security-tips {
    background: #2a2a2a;
    color: #fff;
  }

  .input-wrapper {
    background: #3a3a3a;

    &:focus-within {
      background: #2a2a2a;
    }
  }

  .page-title {
    color: #fff;
  }

  .tip-text,
  .input-label {
    color: #ccc;
  }
}
</style>
