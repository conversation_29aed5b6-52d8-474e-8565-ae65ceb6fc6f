<template>
  <div>
    <template v-for="item in list" :key="item.id">
      <wd-card @click="handleClick(item)" class="box-shadow">
        <div class="f-y-center">
          <wd-img class="mar-R20" :width="30" :height="30" :src="handlerImage(item.path3.split(',')[0])" />
          <view class="text-nowrap-2 f-1">{{ current == '已处理' ? item.remark2 : item.remark1 }}</view>
        </div>
        <template #title>
          <text>{{ item.subdistric }}-</text>
          <text>{{ item.task_Type }}</text></template
        >
        <template #footer>{{ item.inputdate.slice(0, 16).replace('T', ' ') }}</template>
      </wd-card>
    </template>
  </div>
  <div class="back-white border-R14 box-shadow">
    <wd-status-tip image="search" v-if="!list.length" tip="无数据" />
  </div>
  <div style="padding: 80rpx"></div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { handlerImage } from '/src/utils/index.js'
import { getOperationFeedbackZoneCodelApi } from '/src/services/model/submit.js'
import uniUtil from '/src/utils/uniUtil.js'
import { userHomeStore } from '/src/store/home.js'
const { deliveryDots } = userHomeStore()

const props = defineProps({ code: String })

watch(() => props.code, getList, { immediate: true })

const list = ref([])
watch(list, (val) => deliveryDots(val, 'gis'))

async function getList(zoneCode) {
  try {
    const result = await getOperationFeedbackZoneCodelApi(zoneCode)
    list.value = result.data.operationFeedbacks
  } catch (error) {
    list.value = []
  }
}
function handleClick(value) {
  uniUtil.navigateTo(`/src/pages/mine/children/record/detail?id=${value.id}`)
}
</script>
