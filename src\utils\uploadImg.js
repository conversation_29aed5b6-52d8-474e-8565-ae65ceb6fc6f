export const uploadImage = (zoneCode) => {
 return new Promise((resolve, reject) => {
  uni.showLoading({
   mask: true
  })
  uni.chooseImage({
   count: 1, // 最多可选择的图片数量
   sizeType: ['compressed'], // 压缩图片
   sourceType: ['album', 'camera'], // 选择图片的来源，相册或相机
   success: (res) => {
    const tempFilePaths = res.tempFilePaths
    // 上传图片到服务器
    uni.uploadFile({
     url: `https://www.szwgft.cn:5000/api/UpWrite/upload?Zone_Code=${zoneCode}`,
     filePath: tempFilePaths[0],
     name: 'file',
     success: (uploadRes) => {
      const url = JSON.parse(uploadRes.data).data
      uni.hideLoading()
      resolve(url)
     },
     fail: (error) => {
      uni.hideLoading()
      reject(error.errMsg)
     }

    })
   },
   fail: (error) => {
    uni.hideLoading()
    console.log(error.errMsg)
    // 选择图片失败处理逻辑
   },

  })
 })
}

function handlerImage(url) {
 const path = "https://www.szwgft.cn:8090" + '/' + url
 return path
}