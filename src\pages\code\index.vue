<template>
  <div class="all back-white flex f-column">
    <wd-search v-model="value" @search="search" />
    <div class="f-1">
      <MapBox :variation />
    </div>
    <ControlLayer @change="layerChange" :layerList style="top: 320rpx; right: 30rpx" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import UploadeImg from '/src/components/UploadeImg/index.vue'
import { imageBaseUrl, ServerBaseUrl } from '/src/config/index.json'
import MapBox from './compoents/MapBox/index.vue'
import ControlLayer from '/src/components/ControlLayer/index.vue'

const variation = ref(null)

const layerList = ref([
  {
    layerId: '图层1',
    name: '图层1',
    checked: true
  },
  {
    layerId: '图层2',
    name: '图层2'
  },
  {
    layerId: '图层3',
    name: '图层3'
  },
  {
    layerId: '图层4',
    name: '图层4'
  }
])

function search({ value }) {
  variation.value = { type: 'search', value }
}

function layerChange(e) {
  console.log(e)
}
</script>

<style lang="less" scoped></style>
