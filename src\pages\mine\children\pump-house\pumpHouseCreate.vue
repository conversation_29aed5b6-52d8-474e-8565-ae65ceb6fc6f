<template>
  <div class="pumpHouseUpdate pad-24">
    <wd-cell-group custom-class="mar-B22  box-shadow border-R12 overflow-hidden" title="基础信息" border>
      <wd-input required label="泵房名称" label-width="220rpx" show-word-limit prop="PumpHouseName" suffix-icon="warn-bold" clearable v-model="detail.PumpHouseName" placeholder="请输入泵房名称" />
      <wd-picker required label="改造状态" placeholder="选择状态" label-width="220rpx" prop="RemouldState" v-model="detail.RemouldState" :columns="selectOption.GZ" />
      <wd-picker required label="泵房批次" placeholder="选择批次" label-width="220rpx" prop="Batch" v-model="detail.Batch" :columns="selectOption.LX" />
      <wd-input label="加压供水户数" type="number" label-width="220rpx" show-word-limit prop="PressurizedHouseholds" suffix-icon="warn-bold" clearable v-model="detail.PressurizedHouseholds" placeholder="请输入加压户数" />
      <wd-input label="小区建设时间" label-width="220rpx" show-word-limit prop="ConstructionTime" suffix-icon="warn-bold" clearable v-model="detail.ConstructionTime" placeholder="请输入加压户数" />
      <wd-input label="泵房管理状态" label-width="220rpx" show-word-limit prop="PumpRoomControlledState" suffix-icon="warn-bold" clearable v-model="detail.PumpRoomControlledState" placeholder="请输入管理状态" />
    </wd-cell-group>

    <wd-cell-group custom-class="mar-B22  box-shadow border-R12 overflow-hidden" title="物业信息" border>
      <wd-input label="物业单位" label-width="220rpx" show-word-limit prop="PropertyUnit" suffix-icon="warn-bold" clearable v-model="detail.PropertyUnit" placeholder="请输入物业管理单位" />
      <wd-input label="物业联系人" label-width="220rpx" show-word-limit prop="ContactPerson" suffix-icon="warn-bold" clearable v-model="detail.ContactPerson" placeholder="请输入联系人" />
      <wd-input label="物业电话" label-width="220rpx" show-word-limit prop="PhoneNumber" suffix-icon="warn-bold" clearable v-model="detail.PhoneNumber" placeholder="请输入电话" />
    </wd-cell-group>

    <wd-cell-group custom-class="mar-B22  box-shadow border-R12 overflow-hidden" title="项目信息" border>
      <wd-picker label="项目进展状态" placeholder="选择进展状态" label-width="250rpx" prop="ProgressStatus" v-model="detail.ProgressStatus" :columns="selectOption.ZT" />
      <wd-picker required label="运营管理状态" placeholder="选择管理状态" label-width="220rpx" prop="OperationManagementState" v-model="detail.OperationManagementState" :columns="selectOption.YY" />

      <wd-input label="施工单位" label-width="250rpx" show-word-limit prop="ConstructionUnit" suffix-icon="warn-bold" clearable v-model="detail.ConstructionUnit" placeholder="请输入施工单位" />
      <wd-input label="现场监管责任人" label-width="250rpx" show-word-limit prop="PersonInCharge" suffix-icon="warn-bold" clearable v-model="detail.PersonInCharge" placeholder="请输入责任人" />
      <wd-input label="临供停水事件数" label-width="250rpx" show-word-limit prop="TemporarySupplyEvents" suffix-icon="warn-bold" clearable v-model="detail.TemporarySupplyEvents" placeholder="请输入停水数" />
      <wd-calendar label="初步验收时间" label-width="250rpx" v-model="detail.AcceptanceTime" />
      <wd-textarea label="备注" auto-height type="textarea" v-model="detail.Remark" :maxlength="200" show-word-limit placeholder="请输入备注信息" clearable prop="Remark" />
    </wd-cell-group>

    <wd-cell-group custom-class="mar-B22  box-shadow relative border-R12 overflow-hidden" title="位置信息" border>
      <wd-cell title="坐标" :value="detail.X + ',' + detail.Y" />
      <wd-cell title="所属街道" :value="detail.BelongingStreet" />
      <wd-cell title="所属片区" prop="belongingArea" :value="detail.BelongingArea" />
      <wd-cell title="所属网格" :value="detail.Gridding" />
      <wd-textarea label="小区地址" auto-height type="textarea" v-model="detail.ResidentialAddress" :maxlength="200" show-word-limit placeholder="请输入地址信息" clearable prop="ResidentialAddress" />
      <wd-textarea required label="泵房精确位置" auto-height type="textarea" v-model="detail.AccuratePosition" :maxlength="200" show-word-limit placeholder="规则：xxx小区xxx栋地下二层" clearable prop="AccuratePosition" />
      <div class="updateIcon absolute" @click="open = true">
        <wd-icon name="location" size="16px" color="#fff"></wd-icon>
      </div>
      <!-- <div style="border-top: 2rpx solid #ebedf0" class="pad-24">
        <div class="">泵房图片</div>
        <div>
          <UploadImage :maxlength="3" :url v-model="detail.PumpHouseImg" />
        </div>
      </div> -->
    </wd-cell-group>

    <!-- <wd-cell-group custom-class="mar-B22 border-R12 overflow-hidden" title="节点信息" border v-if="detail">
      <wd-tabs v-model="CurrentNode">
        <block v-for="(item, index) in pumpHouseNodeKeys" :key="item">
          <wd-tab :title="`${item}`">
            <div style="min-height: 400rpx">
              <div v-if="pumpHouseNodeDetail">
                <wd-cell title="是否完成" title-width="100px" prop="switchVal" center>
                  <wd-radio-group v-model="pumpHouseNodeDetail.IsEnd" shape="button">
                    <wd-radio :value="true">是</wd-radio>
                    <wd-radio :value="false">否</wd-radio>
                  </wd-radio-group>
                </wd-cell>
                <wd-calendar label="时间" v-model="pumpHouseNodeDetail.CompletionTime" />
                <wd-textarea label="备注" auto-height label-width="100px" type="textarea" v-model="pumpHouseNodeDetail.Remark" :maxlength="200" show-word-limit placeholder="无备注信息" clearable prop="content" />
                <div class="pad-X24 mar-B30" style="border-top: 2rpx solid #ebedf0" v-if="pumpHouseNodeDetail?.Files">
                  <div class="pad-Y16 fon-W600" style="border-bottom: 2rpx solid #ebedf0">该节点需上传的文件</div>
                  <template v-for="fileItem in pumpHouseNodeFileKeyMap.get(index + 1)" :key="fileItem.Id">
                    <div class="flex f-y-center border-B-eee f-between pad-R24">
                      <div class="fon-S24 fon-W600 mar-Y10" :class="{ 'color-999': !pumpHouseNodeDetail.Files.includes(fileItem.DictCode) }" style="padding: 6rpx 0">{{ fileItem.DictValue }}</div>
                      <yt-upload
                        v-if="!pumpHouseNodeDetail.Files.includes(fileItem.DictCode)"
                        childId="upload1"
                        height="50rpx"
                        width="50rpx"
                        :option="{
                          url: `https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${detail.PumpRoomNumber}/${index + 1}`,
                          name: 'file',
                          header
                        }"
                        :size="120"
                        :debug="false"
                        :instantly="true"
                        @progress="progress"
                        @uploadEnd="(e) => uploadEnd(e, index + 1, fileItem.DictCode)"
                      >
                        <div class="f-xy-center border-R10" style="background-color: #1677ff">
                          <wd-icon name="upload" color="#fff" size="26px"></wd-icon>
                        </div>
                      </yt-upload>
                    </div>
                  </template>
                </div>
              </div>
              <div v-else class="relative">
                <wd-status-tip image="content" tip="暂无节点内容" />
                <div class="absolute flex f-column f-xy-center" @click="addNode(index + 1)" style="bottom: 2%; right: 4%">
                  <wd-icon name="add" size="28px" color="#0078d4"></wd-icon>
                  <div class="fon-S22">添加节点信息</div>
                </div>
              </div>
            </div>
          </wd-tab>
        </block>
      </wd-tabs>
    </wd-cell-group> -->
    <wd-button custom-class=" W100 mar-B30" icon="check-bold" @click="handelCreateClick">创建</wd-button>

    <CoordinatePickup v-if="detail && open" v-model="open" :MarkerCenter @change="change" />

    <div class="wrap f-xy-center" v-if="show">
      <div class="content pad-14 f-y-center f-between f-column">
        <div class="text-center mar-10 fon-W600">上传中</div>
        <wd-loading />
        <wd-progress :percentage hide-text />
      </div>
    </div>
  </div>
  <wd-toast />
</template>

<script setup>
import { ref, reactive, onMounted, toRaw, computed, watch } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { PumpHouseApi } from '/src/services/model/pump.house.js'
import { CommonApi } from '/src/services/model/common.js'
import CoordinatePickup from './components/CoordinatePickup/index.vue'
import uniUtil from '/src/utils/uniUtil.js'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const pumpHouseNode = ref([])
const open = ref(false)
const show = ref(false)
const MarkerCenter = ref(null)
const CurrentNode = ref(0)
const percentage = ref(0)
const detail = ref({
  BelongingArea: null,
  PumpHouseName: null,
  RemouldState: null,
  Gridding: null,
  ResidentialAddress: null,
  BelongingStreet: null,
  ConstructionTime: null,
  PressurizedHouseholds: null,
  PropertyUnit: null,
  ContactPerson: null,
  PhoneNumber: null,
  PumpRoomControlledState: null,
  ProgressStatus: '正常',
  OperationManagementState: null,
  ConstructionUnit: null,
  PersonInCharge: null,
  Remark: null,
  TemporarySupplyEvents: null,
  AcceptanceTime: null,
  X: null,
  Y: null,
  ZoneCode: null,
  AccuratePosition: null,
  CurrentNode: 1,
  Attackers: null,
  Batch: null,
  PumpHouseImg: ''
})

const selectOption = ref({})
// const pumpHouseNodeKeys = computed(() => pumpHouseNode.value.map((item) => item.DictValue))
const header = computed(() => ({ authorization: uniUtil.get('userInfo')?.token }))
const url = computed(() => `https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${detail.value.PumpRoomNumber}/img`)
onLoad(async (e) => {
  const { lngLat, Belong_Are, Grid, code, Subdistric } = e
  const { lng, lat } = JSON.parse(lngLat)
  detail.value.X = lng.toString()
  detail.value.Y = lat.toString()
  detail.value.Gridding = Grid.replace('FD', '福东').replace('FZ', '福中').replace('ML', '梅林').replace('XM', '香蜜')
  detail.value.BelongingStreet = Subdistric
  detail.value.BelongingArea = Belong_Are
  detail.value.ZoneCode = code

  pumpHouseNode.value = await queryDictionaries('pumpHouseNode')
  CurrentNode.value = detail.value.CurrentNode - 1
  MarkerCenter.value = [lng, lat]
})

onShow(async () => {
  const pumpHouseProjectStatus = await queryDictionaries('pumpHouseProjectStatus') //项目状态
  selectOption.value.ZT = pumpHouseProjectStatus.map((item) => ({ label: item.DictValue, value: item.DictValue }))
  const pumpHouseProjectBatch = await queryDictionaries('pumpHouseProjectBatch')
  selectOption.value.LX = pumpHouseProjectBatch.map((item) => ({ label: item.DictValue, value: item.DictCode })) //项目状态
  const pumpHouseOperationState = await queryDictionaries('pumpHouseOperationState')
  selectOption.value.YY = pumpHouseOperationState.map((item) => ({ label: item.DictValue, value: item.DictValue })) //项目状态
  const pumpHouseRemouldState = await queryDictionaries('pumpHouseRemouldState')
  selectOption.value.GZ = pumpHouseRemouldState.map((item) => ({ label: item.DictValue, value: item.DictValue })) //项目状态
})

async function queryDictionaries(type) {
  try {
    const { data } = await CommonApi.queryDictionaries(type)
    return data
  } catch (error) {
    message.error(error.message)
  }
}

function change(e) {
  detail.value.X = e.x.toString()
  detail.value.Y = e.y.toString()
  detail.value.Gridding = e.feature.Grid.replace('FD', '福东').replace('FZ', '福中').replace('ML', '梅林').replace('XM', '香蜜')
  detail.value.BelongingStreet = e.feature.Subdistric
  detail.value.BelongingArea = e.feature.Belong_Are
  open.value = false
  MarkerCenter.value = [e.x, e.y]
}

async function handelCreateClick() {
  try {
    const keys = ['Batch', 'RemouldState', 'PumpHouseName', 'OperationManagementState', 'AccuratePosition']
    const isPass = keys.every((key) => detail.value[key] && detail.value[key] !== '')
    if (!isPass) return toast.error('请填写完整信息')
    if (detail.value.AcceptanceTime) detail.value.AcceptanceTime = dayjs(detail.value.AcceptanceTime).format('YYYY-MM-DD')
    await PumpHouseApi.create(detail.value)
    toast.success('泵房添加成功')
    setTimeout(() => uni.navigateBack(), 1500)
  } catch (error) {
    toast.error('泵房添加失败')
  }
}
</script>

<style lang="less" scoped>
.updateIcon {
  top: 16rpx;
  right: 26rpx;
  padding: 4rpx 18rpx;
  background: #15a131;
  border-radius: 12rpx;
}
:deep(.wd-tabs__map-nav-btn) {
  width: auto;
  height: auto;
  padding: 8rpx 18rpx;
  margin-bottom: 10rpx;
  line-height: normal;
}

.wrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
  .content {
    background-color: white;
    height: 200rpx;
    width: 400rpx;
    border-radius: 12rpx;
  }
}
</style>
