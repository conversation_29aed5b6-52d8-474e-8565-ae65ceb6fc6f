<template>
  <view class="all" id="DOMID" />
  <view :variation :change:variation="map.watchVariationChange" />
</template>

<script>
import uniUtil from '/src/utils/uniUtil'

export default {
  emits: ['markerClick', 'mapClick', 'mapLoad', 'wdToast', 'openPopup', 'queryFeatures'],
  props: { variation: { type: Object, default: null } },
  methods: {
    // 地图事件接收器
    deliveryEvent(e) {
      if (e.delivery) return this.$emit(e.type, e.value)
      this[e.type](e.value)
    },
    markerClick(value) {
      const { Subdistric, code, lnglat, name } = value
      uniUtil.navigateTo(`/src/pages/mine/children/record/create?lngLat=${JSON.stringify(lnglat)}&code=${code}&name=${name}&Subdistric=${Subdistric}`)
    }
  }
}
</script>

<script module="map" lang="renderjs">
import mapboxgl from 'mapbox-gl'
import basicsConfig from './config.js'
import uniUtil from '../../../../../../utils/uniUtil.js'

export default {
  data() {
    return { Map: null, Marker: null  }
  },
  mounted() {
    this.initMap()
  },
  beforeUnmount(){
    // 销毁地图实例
    this.Map?.remove()
    this.Map = null
    this.Marker = null
  },

  methods: {
    // 事件触发接收器
    watchVariationChange(e){
      if(!e)return
      this[e.type](e.value)
    },

    // 初始化地图
    initMap(options = {}) {
      const accessToken = options?.accessToken ?? basicsConfig.map.accessToken
      const mapConfig = options?.map ?? {}
      mapboxgl.accessToken = accessToken
      const mapOptions = { container: 'DOMID', ...basicsConfig.map, ...mapConfig }
      this.Map = new mapboxgl.Map(mapOptions)
      this.Map.on('load', () => {
        this.addDotEvent()
        this.$ownerInstance.callMethod('deliveryEvent', {type: 'mapLoad', value:null, delivery:true})
      })
    },

    // 添加地图icon
    loadingImages(imagesOptions) {
      const images = Object.entries(imagesOptions)
      images.forEach(([imageName, imageUrl]) => {
        this.Map.loadImage(imageUrl, (error, image) => {
          if (error) {
            console.error('Error loading image:', imageName, error)
            return
          }
          // 检查图片是否已存在，如果存在则先移除
          if (this.Map.hasImage(imageName)) {
            this.Map.removeImage(imageName)
          }
          this.Map.addImage(imageName, image)
        })
      })
    },


    // 更新数据
    updatedDot(data) {
      if (!data.length) return
      const source = this.Map.getSource('dot-source')
      if(!source) return
      const updatedGeojsonData = { type: 'FeatureCollection', features: data }
      source.setData(updatedGeojsonData)
    },

    reach(data) {
      if (!data) return
      this.Map.flyTo({ center: data.Center_Point, zoom: 17, curve: 1.5, duration: 2500 })
      this.Map.setFilter('zone-line2', ['match', ['get', 'Zone_Name'], data.name, true, false])
    },

    //小区档案图层点击事件处理函数
    addDotEvent(){
      this.Map.on('click', 'point', (e) => {
        const [feature] = this.Map.queryRenderedFeatures(e.point, { layers: ['point'] })
        const { properties } = feature
        this.$ownerInstance.callMethod('deliveryEvent', {type:'mapClick', value:properties, delivery:true})
      })
    },

    moveto(center){
      const data = [{ type: 'Feature', id: 0, properties: {}, geometry: { type: 'Point', coordinates: center } }]
      const source = this.Map.getSource('oneself-source')
      const updatedGeojsonData = { type: 'FeatureCollection', features: data }
      source.setData(updatedGeojsonData)
      this.Map.flyTo({ center:center, zoom: 17.8, duration: 1500 })
    },

    // 向地图添加总阀总表Marker
    addMapMarker(){
      if (this.Marker) {
        this.Marker.remove()
        this.Marker = null
        this.Map.off('move', this.handlerMoveEvent)
        return
      }else{
        this.handlerMoveEvent = () => { this.Marker.setLngLat(Object.values(this.Map.getCenter())) }
        this.Map.on('move', this.handlerMoveEvent)
        this.Marker = new mapboxgl.Marker().setLngLat(Object.values(this.Map.getCenter())).addTo(this.Map)
        this.handlerMarkerClick =(event) => {
        event.stopPropagation()
        this.Map.off('move', this.handlerMoveEvent)
        // 查询区块要素
        const [feature] = this.Map.queryRenderedFeatures([this.Marker._pos.x, this.Marker._pos.y], { layers: ['zone-fill']})
        const { Zone_Code, Zone_Name,Subdistric } = feature?.properties ?? { Zone_Code: '超出边界', Subdistric: '超出边界', Zone_Name: '超出边界' }
        const data = {code : Zone_Code, name: Zone_Name, Subdistric,lnglat:this.Marker._lngLat}
        this.$ownerInstance.callMethod('deliveryEvent', {type:'markerClick', value:data})
        this.Marker.getElement().removeEventListener('click', this.handlerMarkerClick)
        this.Marker.remove()
        this.Marker = null
      }
        // 标记添加点击事件
        this.Marker.getElement().addEventListener('click', this.handlerMarkerClick)
      }

    },

    // 添加图层颜色
    setFillColor(params){
    const codes = new Set(params.map((item) => item.properties.zone_Code))
    this.Map.setPaintProperty('zone-fill2', 'fill-color', ['case', ['in', ['get', 'Zone_Code'], ['literal', [...codes]]], '#5cdb54', '#1e6fff'])
    },

    // 图层控制
    layerChange(params) {
      this.Map.setLayoutProperty(params.layerId, 'visibility',params.checked? 'visible':'none')
    },

    renderMoveend (){
      this.Map.fire('moveend')
    }
  }
}
</script>
