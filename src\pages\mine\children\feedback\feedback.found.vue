<template>
  <div class="all flex f-column f-between pad-B36">
    <div class="content pad-24 border-R16">
      <div class="back-white border-R16 overflow-hidden box-shadow">
        <wd-textarea v-model="formData.FeedbackContent" :maxlength="300" placeholder="请详细描述问题" clearable show-word-limit />

        <div class="flex">
          <template v-for="(url, index) in images" :key="url">
            <div class="img_box relative">
              <div class="absolute img_icon">
                <wd-icon class="absolute img_icon" @click="images.splice(index, 1)" name="close-circle-filled" color="#fc3434" size="18px"></wd-icon>
              </div>
              <image class="img" :src="url" @click="previewImage(url)" mode="aspectFill"></image>
            </div>
          </template>
        </div>

        <div class="pad-24">
          <UploadImage url="https://www.szwgft.cn/nodeServer/resource/upload/feedback" @success="handleUploadSuccess">
            <wd-icon name="picture" color="#333" size="24px" />
          </UploadImage>
          <div class="color-999 fon-S22">JPG、PNG、BMP、格式，2M以内，建议在WLAN下上传</div>
        </div>
      </div>
      <div class="pad-24 mar-T30 back-white border-R16 f-xy-center box-shadow">
        <div class="f-1" @click="sheetOpen = true">
          <div class="fon-W600">反馈类型</div>
          <div class="mar-T10" style="color: #1d70f5">
            <div class="" v-if="!formData.FeedbackType">请选择</div>
            <div class="" v-else>{{ actions.find((item) => item.value === formData.FeedbackType).name }}</div>
          </div>
        </div>
        <wd-icon name="arrow-right" color="#999" size="22px"></wd-icon>
      </div>
    </div>
    <div class="pad-24 mar-B30">
      <wd-button custom-class="fixed W100 bottom-0 left-0 right-0" type="primary" @click="handleSubmit" size="large">提交</wd-button>
    </div>
  </div>
  <wd-action-sheet v-model="sheetOpen" :actions="actions" @select="handleSelect" />
  <wd-toast />
</template>
<script setup>
import { ref, reactive } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { CommonApi } from '/src/services/model/common.js'
import { feedbackIssueApi } from '/src/services/model/feedback.issue.js'
import { useToast } from 'wot-design-uni'
import UploadImage from './components/UploadeImage/index.vue'
import { cache } from '/src/utils/cache.js'

const toast = useToast()
const sheetOpen = ref(false)
const formData = reactive({ FeedbackType: null, FeedbackContent: null, FeedbackUserID: null, FeedbackImg: '' })
const userInfo = cache.get('userInfo')
onShow(() => getFeedbackTypes())

const actions = ref([])

function handleSelect({ item }) {
  formData.FeedbackType = item.value
}

async function getFeedbackTypes() {
  try {
    const { data } = await CommonApi.queryDictionaries('feedback')
    actions.value = data.map((item) => ({ name: item.DictValue, value: item.DictCode }))
  } catch (error) {
    toast.error('获取反馈类型失败')
  }
}

async function handleSubmit() {
  if (!formData.FeedbackContent) return toast.warning('请输入反馈内容')
  if (!formData.FeedbackType) return toast.warning('请选择反馈类型')
  formData.FeedbackImg = images.value.join(',')
  formData.FeedbackUserID = userInfo.id
  try {
    await feedbackIssueApi(formData)
    toast.success('提交成功')
    setTimeout(() => uni.navigateBack(), 1000)
  } catch (error) {
    toast.error('提交失败')
  }
}

const images = ref([])
function handleUploadSuccess(e) {
  images.value.push(e.data)
}

function previewImage(current) {
  uni.previewImage({ urls: images.value, current })
}
</script>

<style lang="less" scoped>
.img_box {
  width: 90rpx;
  height: 90rpx;
  margin: 0 12rpx;
  border: 2rpx solid #e5e5e5;
  .img {
    width: 90rpx;
    height: 90rpx;
  }
  .img_icon {
    right: -15rpx;
    top: -10rpx;
    z-index: 9;
  }
}
</style>
