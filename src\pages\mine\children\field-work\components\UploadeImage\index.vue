<template>
  <canvas class="canvas" canvas-id="mycanvas" id="mycanvas" :style="canvasStyle"> </canvas>
  <div class="" @click="uploadFile"><slot></slot></div>
  <wd-toast />
</template>

<script setup>
import { ref } from 'vue'
import { getCurrentInstance } from 'vue'
import { cache } from '/src/utils/cache'
import { useToast } from 'wot-design-uni'

const instance = getCurrentInstance()
const emit = defineEmits(['success'])
const props = defineProps({
  url: { type: String, default: '' },
  header: { type: Object, default: () => ({}) },
  quality: { type: Number, default: 0.32 }
})

const toast = useToast()
const canvasStyle = ref({
  width: '2000px',
  height: '2000px',
  position: 'absolute',
  top: '-9999px',
  left: '-9999px'
})

async function uploadFile() {
  try {
    // 先让用户选择图片来源
    const actionSheetRes = await uni.showActionSheet({
      itemList: ['从相册选择', '拍照']
    })

    let sourceType = []
    let isCamera = false

    if (actionSheetRes.tapIndex === 0) {
      sourceType = ['album']
    } else if (actionSheetRes.tapIndex === 1) {
      sourceType = ['camera']
      isCamera = true
    }

    const chooseImageRes = await uni.chooseImage({
      count: 1,
      sourceType: sourceType
    })

    // 只有相机拍摄时才保存到相册
    if (isCamera && chooseImageRes.tempFilePaths && chooseImageRes.tempFilePaths.length > 0) {
      try {
        await uni.saveImageToPhotosAlbum({
          filePath: chooseImageRes.tempFilePaths[0]
        })
        toast.success('照片已保存到相册')
      } catch (saveError) {
        // 保存失败可能是权限问题，但不影响上传流程
        console.log('保存到相册失败:', saveError)
        if (saveError.errMsg && saveError.errMsg.includes('auth')) {
          toast.error('需要相册权限才能保存图片')
        }
      }
    }

    toast.loading('解析中')
    const { width, height, path } = await uni.getImageInfo({ src: chooseImageRes.tempFilePaths[0] })

    // 动态设置Canvas尺寸
    canvasStyle.value = {
      width: `${width}px`,
      height: `${height}px`,
      position: 'absolute',
      top: '-9999px',
      left: '-9999px'
    }

    // 等待下一个tick确保Canvas尺寸更新
    await new Promise((resolve) => setTimeout(resolve, 100))

    const ctx = uni.createCanvasContext('mycanvas', instance)
    ctx.drawImage(path, 0, 0, width, height)
    ctx.draw(false, async () => {
      const { tempFilePath } = await uni.canvasToTempFilePath({ width, height, fileType: 'jpg', canvasId: 'mycanvas', quality: props.quality }, instance)
      const token = cache.get('userInfo')?.token
      const header = { Authorization: token ? `Bearer ${token}` : '', ...props.header }
      const { data } = await uni.uploadFile({ url: props.url, filePath: tempFilePath, header, name: 'file' })
      const res = JSON.parse(data)
      emit('success', res)
      toast.close()
    })
  } catch (error) {
    console.log(error)
    toast.close()
  }
}
</script>

<style lang="less" scoped>
/* Canvas尺寸现在通过动态样式控制，无需固定样式 */
</style>
