<template>
  <div class="all flex f-column">
    <wd-search @search="debounceHandlesearchChange" @change="debounceHandlesearchChange" />

    <div class="f-1">
      <MapGlS :variation @wdToast="handleWdToast" @queryFeatures="handleQueryFeatures" @mapLoad="mapLoad" @mapClick="mapClick" />
    </div>

    <div class="fixed" style="left: 20rpx; bottom: 20rpx">
      <MapLegend :legend />
    </div>
    <ControlLayer @change="layerChange" class="z-index-10" :layerList style="bottom: 280rpx; right: 50rpx" />
    <div class="fixed box-shadow f-xy-center pad-10 back-white border-R12" @click="addInformation" style="bottom: 190rpx; right: 50rpx"><wd-icon name="add" color="#2d2d2d" size="22px"></wd-icon></div>
    <div class="fixed box-shadow f-xy-center pad-10 back-white border-R12" @click="handleOneself" style="bottom: 100rpx; right: 50rpx">
      <image style="width: 44rpx; height: 44rpx" src="/static/DW.png"></image>
    </div>

    <PopupMenu v-model="show">
      <template #menu>
        <div class="f-xy-center fon-W700 fon-S28">{{ detail.path4.length ? '已修改' : '未修改' }}</div>
      </template>
      <template #content>
        <PopupDetali :detail @clickBack="show = -1" />
      </template>
    </PopupMenu>
  </div>

  <!-- 搜索列表弹窗 -->
  <wd-popup :modal="false" v-model="searchOpen" closable position="bottom" custom-style="min-height: 1000rpx; z-index:99">
    <div class="pad-24">
      <div class="pad-Y32"></div>
      <div class="flex f-between" style="background-color: #32cbdb">
        <div class="f-1 pad-16 text-center color-white">小区名称</div>
        <div class="f-1 pad-16 text-center color-white">小区编码</div>
      </div>
      <div class="overflow-auto" style="max-height: 800rpx">
        <template v-for="(item, index) in searchList" :key="index">
          <div class="item pad-16 mar-Y12 border-B-eee flex f-between" @click="searchItemClick(item)">
            <div class="f-1 text-center">{{ item.name }}</div>
            <div class="f-1 text-center">{{ item.Zone_Code }}</div>
          </div>
        </template>
        <wd-status-tip v-if="searchList.length == 0" image="search" tip="当前搜索无结果" />
      </div>
    </div>
  </wd-popup>
  <FeedbackPopup ref="feedbackRef" :id="6" />

  <wd-toast />
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { onShow, onNavigationBarButtonTap, onBackPress } from '@dcloudio/uni-app'

import MapGlS from './components/MapGlS/index.vue'
import PopupMenu from '/src/components/PopupMenu/index.vue'
import PopupDetali from './components/PopupDetali.vue'
import MapLegend from '/src/components/MapLegend/index.vue'
import debounce from 'lodash/debounce'

import { useToast } from 'wot-design-uni'

import ControlLayer from '/src/components/ControlLayer/index.vue'

import useMapDotStore from '/src/store/mapDot.js'
import { getZoneDataLike } from '/src/services/model/map.js'
import { CommonApi } from '/src/services/model/common'
import { VerifyPipeApi } from '/src/services/model/verify.js'
import FeedbackPopup from '/src/components/FeedbackPopup/index.vue'
import { getOperationFeedbackDetailApi } from '/src/services/model/submit.js'

import { storeToRefs } from 'pinia'

const { getGisFaults } = useMapDotStore()
const toast = useToast()

const { gisFaults } = storeToRefs(useMapDotStore())
const variation = ref(null)
const searchOpen = ref(false)

const legend = computed(() => {
  return [
    {
      label: '已处理',
      url: 'https://www.szwgft.cn:8090/AppIcon/1.png',
      value: gisFaults.value.filter((i) => i.properties.icon === '已处理').length,
      unit: '个'
    },
    {
      label: '未处理',
      url: 'https://www.szwgft.cn:8090/AppIcon/0.png',
      value: gisFaults.value.filter((i) => i.properties.icon !== '已处理').length,
      unit: '个'
    }
  ]
})

const feedbackRef = ref(null)

// 监听按钮点击事件
onNavigationBarButtonTap(() => feedbackRef.value.getFeedbackDetail())
onBackPress((e) => {
  const isShow = feedbackRef.value.getFeedbackIsOpen()
  if (show.value != -1 || searchOpen.value === true || isShow) {
    show.value = -1
    searchOpen.value = false
    feedbackRef.value.close()
    return true
  }
})

const show = ref(-1)
onShow(async () => {
  toast.loading('加载中..')
  getGisFaults()
  if (show.value !== -1) {
    const { data } = await getOperationFeedbackDetailApi(detail.value.id)
    detail.value = data.operationFeedbacks[0]
  }
})

watch(gisFaults, (value) => {
  setTimeout(() => (variation.value = { type: 'updatedDot', value }))
  toast.close()
})

const detail = ref(null)
async function mapClick(val) {
  detail.value = val
  searchOpen.value = false
  show.value = 0
}

function mapLoad() {
  // 添加地图icon
  variation.value = {
    type: 'loadingImages',
    value: {
      已处理: 'https://www.szwgft.cn:8090/AppIcon/1.png',
      未处理: 'https://www.szwgft.cn:8090/AppIcon/0.png',
      箅子: 'https://www.szwgft.cn/nodeServer/img/BZ.png',
      雨水井: 'https://www.szwgft.cn/nodeServer/img/YS.png',
      污水井: 'https://www.szwgft.cn/nodeServer/img/WS.png'
    }
  }
}

function addInformation() {
  variation.value = { type: 'addMapMarker', value: true }
}

async function handleOneself() {
  toast.loading('正在获取位置')
  const { latitude, longitude } = await uni.getLocation({ altitude: true })
  variation.value = { type: 'moveto', value: [longitude, latitude] }
  toast.close()
}

const layerList = ref([
  { layerId: 'Comparison2004WS_line', name: '2014污水管' },
  { layerId: 'Comparison2004Manhole_symbol', name: '2014污水井' },
  { layerId: 'tubeLayer_line', name: '2022排水管' },
  { layerId: 'canalLayer_line', name: '2022排水渠' },
  { layerId: 'well_symbol', name: '2022排水井' },
  { layerId: 'grate_symbol', name: '2022雨水箅子' }
])

// 图层控制
function layerChange(e) {
  variation.value = { type: 'layerChange', value: e }
}

function handleWdToast(value) {
  toast[value.Fn](value.text)
}

async function handleQueryFeatures(list) {
  try {
    if (!list.length) return
    const { data } = await VerifyPipeApi.distinguish({ list })
    const codes = data.filter((item) => item.NowMaterial && item.SceneImg).map((item) => item.SystemId)
    variation.value = { type: 'setPipeColor', value: codes }
  } catch (error) {
    toast.error('查询失败')
  }
}

// 搜索
const searchList = ref([])
const debounceHandlesearchChange = debounce(searchChange, 500)
async function searchChange({ value }) {
  if (!value) return
  const res = await getZoneDataLike(value)
  const result = JSON.parse(res.data)
  searchList.value = result.map((i) => ({ name: i.Zone_Name, Zone_Code: i.Zone_Code, Center_Point: i.Center_Point.split(',') }))
  searchOpen.value = true
}

// 搜索内容点击
function searchItemClick(item) {
  variation.value = { type: 'reach', value: item }
  searchOpen.value = false
}
</script>

<style lang="less" scoped>
:deep(.custom-button) {
  min-width: auto !important;
  box-sizing: border-box;
  width: 32px !important;
  height: 32px !important;
  border-radius: 16px !important;
  margin: 8rpx;
}

:deep(.custom-radio) {
  height: 32px !important;
  line-height: 32px !important;
}

:deep(.wd-checkbox) {
  margin-bottom: 6rpx;
}

:deep(.wd-checkbox__txt) {
  font-size: 20rpx;
  font-weight: 600;
}

.search-type {
  position: relative;
  height: 30px;
  line-height: 30px;
  padding: 0 8px 0 16px;
}

.search-type::after {
  position: absolute;
  content: '';
  width: 1px;
  right: 0;
  top: 5px;
  bottom: 5px;
  background: rgba(0, 0, 0, 0.25);
}

.search-type {
  :deep(.icon-arrow) {
    display: inline-block;
    font-size: 20px;
    vertical-align: middle;
  }
}

.item {
  background-color: #eee;
}
.item:nth-child(2n) {
  background-color: antiquewhite;
}
</style>
