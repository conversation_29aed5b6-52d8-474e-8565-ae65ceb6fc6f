<template>
  <div class="zone-record all">
    <wd-navbar>
      <template #left>
        <wd-picker title="水务所筛选" :columns="columns" v-model="seleteValue" @confirm="handleConfirm" />
      </template>
      <template #right>
        <wd-search v-model="keywordName" custom-class="search" hide-cancel placeholder-left @search="searchKeywordName" />
      </template>
    </wd-navbar>

    <div class="record_content f-1 overflow-auto">
      <template v-for="(item, index) in recordList" :key="index + item.Xqbm">
        <div @click="ItemClick(item)" class="fiche mar-B16 pad-24 back-white box-shadow border-R10">
          <div class="fiche-header f-between">
            <div class="fon-S28 fon-W600 color-primary">{{ item.Xqmc }}</div>
            <div class="status-tag">{{ item.Sws }}</div>
          </div>
          <div class="fiche-content f-between mar-T16">
            <div class="info-item">
              <div class="info-label">小区编码</div>
              <div class="info-value">{{ item.Xqbm }}</div>
            </div>
            <div class="arrow-icon">
              <wd-icon name="arrow-right" color="#999" size="18px"></wd-icon>
            </div>
          </div>
        </div>
      </template>

      <div v-if="recordList.length === 0" class="all f-xy-center">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </div>
    </div>
    <wd-pagination v-model="currentPagesNum" :total="total" :pageSize="pageSize" @change="handlePaginationChange" show-icon />
  </div>
  <wd-toast />
</template>

<script setup>
import { ref, watch } from 'vue'
import { onLoad, onShow, onHide } from '@dcloudio/uni-app'
import { zoneRecordList, getFuzzySearch } from '/src/services/model/material.js'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const currentPagesNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const keywordName = ref('')
const columns = ref(['全部', '梅林', '福东', '福中', '香蜜'])
const seleteValue = ref('全部')

onLoad(() => getZoneRecordList(1))
const recordList = ref([])
async function getZoneRecordList(PageNumber, sws) {
  try {
    toast.loading('正在加载...')
    const { Data, Page } = await zoneRecordList(PageNumber, sws)
    total.value = Page.TotalCount
    recordList.value = Data
    toast.close()
  } catch (error) {
    toast.close()
    toast.error(error.message)
  }
}

// 页码改变
function handlePaginationChange({ value }) {
  getZoneRecordList(value)
}

// 模糊搜索
async function searchKeywordName({ value }) {
  try {
    toast.loading('正在加载...')
    const res = await getFuzzySearch(value)
    recordList.value = res.map((item) => ({ Sws: item.sws, Xqmc: item.xqmc, Xqbm: item.xqbm }))
    total.value = recordList.value.length
    currentPagesNum.value = 1
    seleteValue.value = '全部'
    toast.close()
  } catch (error) {
    toast.close()
    toast.error(error.message)
  }
}

watch(keywordName, (value) => {
  if (value === '') {
    getZoneRecordList(1)
    currentPagesNum.value = 1
    seleteValue.value = '全部'
  }
})

function ItemClick(item) {
  uni.navigateTo({ url: `/src/pages/mine/children/material/detail?sws=${item.Sws}&xqmc=${item.Xqmc}&xqbm=${item.Xqbm}` })
}

function handleConfirm({ value }) {
  if (value === '全部') {
    getZoneRecordList(1)
  } else {
    getZoneRecordList(1, value + '水务所')
  }
  currentPagesNum.value = 1
}
</script>

<style lang="less" scoped>
.zone-record {
  display: flex;
  flex-direction: column;
  .record_content {
    padding: 16rpx;
  }
}

.fiche {
  background-color: white;
  border-left: 6rpx solid #32cbdb;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  border-radius: 12rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  }

  .fiche-header {
    display: flex;
    align-items: center;
  }

  .fiche-content {
    display: flex;
    align-items: center;
  }

  .status-tag {
    background-color: rgba(50, 203, 219, 0.1);
    color: #32cbdb;
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    font-weight: 500;
  }

  .info-item {
    display: flex;
    flex-direction: column;
  }

  .info-label {
    font-size: 22rpx;
    color: #999;
    margin-bottom: 4rpx;
  }

  .info-value {
    font-size: 24rpx;
    color: #333;
    font-weight: 500;
  }

  .arrow-icon {
    margin-left: auto;
  }
}

.color-primary {
  color: #32cbdb;
}

.search {
  padding: 0;
}
</style>
