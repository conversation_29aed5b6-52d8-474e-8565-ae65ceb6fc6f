<template>
  <div class="pad-30 back-white fon-W700 fon-S28 mar-B12 border-R12 box-shadow f-xy-center" v-if="detail">{{ detail.xqmc }}</div>
  <wd-cell-group custom-class="mar-B22 box-shadow" border v-if="detail">
    <wd-cell title="组织机构" :value="detail.fgs" />
    <wd-cell title="小区编码" :value="detail.xqbm" />
    <wd-cell title="管理单位" :value="detail.deptname" />
    <wd-cell title="小区类别" :value="detail.xqlb" />
    <wd-cell title="所属街道" :value="detail.ssjd" />
    <wd-cell title="所属社区" :value="detail.communityname" />
    <wd-cell title="行政区" :value="detail.districtname" />
    <wd-cell title="楼宇属性" :value="detail.lcjds" />
  </wd-cell-group>
  <wd-collapse v-model="record" custom-class="collapse" v-if="detail">
    <wd-collapse-item title="小区基本信息" name="小区基本信息">
      <wd-cell-group border>
        <wd-cell label-width="100px" title="性质" :value="detail.nature" />
        <wd-cell title="类型" :value="detail.home_type" />
        <wd-cell title="栋数" :value="handlerCount(detail.block_number)" />
        <div class="pa-X24 mar-X24 f-between f-wrap" v-if="handlerblock(detail.block_number).length">
          <template v-for="(item, index) in handlerblock(detail.block_number)" :key="index">
            <div class="mar-Y4 border-B-eee">{{ item }}</div>
          </template>
        </div>

        <wd-cell title="户数" :value="detail.usercount" />
        <wd-cell title="水厂" :value="detail.water_works" />
        <wd-cell title="是否有泵房" :value="detail.is_pump_room" />
        <wd-cell title="黄海高程" :value="detail.yellow_sea_elevation" />
        <wd-cell title="抄表情况" :value="detail.czccbqk" />
      </wd-cell-group>
    </wd-collapse-item>

    <wd-collapse-item title="供水系统基本信息" name="供水系统基本信息">
      <wd-cell-group border>
        <wd-cell
          title="进水路数"
          :value="
            handlerdata(detail.jsgl)
              .map((item) => item.name)
              .join('、')
          "
        />
        <div>
          <template v-for="(i2, index) in handlerdata(detail.jsgl)" :key="index">
            <div class="pad-X10 mar-Y6 border-eee" v-if="i2.name">
              <div class="fon-S16 fon-W700">{{ i2.name }}</div>
              <template v-for="(i3, code) in Object.entries(i2)" :key="code">
                <div class="fon-S12" v-if="i3[1] && i3[0] != 'name'">{{ keys[i3[0]] }}：{{ i3[1] }}</div>
              </template>
            </div>
          </template>
        </div>
        <wd-cell title="小区供水方式" :value="detail.gsfs" />
        <wd-cell title="利用市政压力" :value="detail.lyszyl" />
        <wd-cell title="加压方式" :value="detail.pressurized_zone" />
        <wd-cell title="管网生消分离情况" :value="detail.gwsxflqk" />
        <wd-cell title="加压分区" :value="detail.jyfq" />
      </wd-cell-group>
    </wd-collapse-item>

    <wd-collapse-item title="供水管网基本信息" name="供水管网基本信息">
      <wd-cell-group border>
        <wd-cell title="初始建设年代" :value="detail.csjsnd" />
        <wd-cell title="是否提标改造" :value="handlerBoolean(detail.sftbgz)" />
        <wd-cell title="改造完成年代" :value="detail.gzwcnd && detail.gzwcnd.slice(0, 10)" />
        <wd-cell title="优饮批次" :value="detail.excellent_drink_batch" />
        <wd-cell title="改造前埋地管材" :value="handlerlist(detail.mdgc)" />
        <wd-cell title="改造后埋地管材" :value="handlerlist(detail.buried_pipes_after)" />
        <wd-cell title="改造前爬墙管材" :value="handlerlist(detail.pqggc)" />
        <wd-cell title="改造后爬墙管材" :value="handlerlist(detail.wall_climbing_pipe_after)" />
        <wd-cell title="常用总表口径(mm)" :value="detail.commonly_used_general_table_caliber" />
      </wd-cell-group>
    </wd-collapse-item>

    <wd-collapse-item title="二次供水基本信息" name="二次供水基本信息">
      <wd-cell-group border>
        <wd-cell title="泵房状态" :value="detail.pump_room_status" />
        <wd-cell title="泵房是否由我司运维" :value="handlerBoolean(detail.is_pump_room_our_company)" />
      </wd-cell-group>
    </wd-collapse-item>

    <wd-collapse-item v-if="isShow.includes(1)" title="泵房改造前（现状）情况" name="泵房改造前（现状）情况">
      <wd-cell-group border>
        <wd-cell title="泵房数量（座）" :value="detail.before_pump_room_count" />
        <wd-cell title="备注" :value="detail.before_remark" />
        <wd-cell title="设计加压规模(m³/日)" :value="detail.before_pressure_scale" />
        <wd-cell title="加压户数" :value="detail.before_number_pressurized" />
        <wd-cell title="供水方式" :value="detail.before_water_supply_method" />
        <wd-cell title="加压分区情况" :value="detail.before_pressurized_partition_situation" />
        <wd-cell title="水泵数量(台)" :value="detail.before_number_pumps" />
        <wd-cell title="水泵材质" :value="detail.before_pump_material" />
        <wd-cell title="水池/箱(座)" :value="detail.before_pool_number" />
        <wd-cell title="水池/箱材质" :value="detail.before_pool_material" />
        <wd-cell title="泵房管路材质" :value="detail.before_pump_room_piping_material" />
        <wd-cell title="泵房阀门主要材质" :value="detail.before_pump_room_valve_main_material" />
      </wd-cell-group>
    </wd-collapse-item>

    <wd-collapse-item v-if="isShow.includes(2)" title="泵房临供情况" name="泵房临供情况">
      <wd-cell-group border>
        <wd-cell title="泵房管路材质" :value="detail.lgbf_pump_room_piping_material" />
        <wd-cell title="泵房阀门主要材质" :value="detail.lgbf_pump_room_valve_main_material" />
        <wd-cell title="临时供水开始时间" :value="detail.egcsjsnd" />
        <wd-cell title="供水方式" :value="detail.lgbf_water_supply_method" />
        <wd-cell title="备注" :value="detail.lgbf_remark" />
      </wd-cell-group>
    </wd-collapse-item>

    <wd-collapse-item v-if="isShow.includes(3)" title="泵房改造后情况" name="泵房改造后情况">
      <wd-cell-group border>
        <wd-cell title="泵房数量（座）" :value="detail.bfsl" />
        <wd-cell title="备注" :value="detail.eg_remark" />
        <wd-cell title="设计加压规模(m³/日)" :value="detail.jygm" />
        <wd-cell title="加压户数" :value="detail.jyhs" />
        <wd-cell title="供水方式" :value="detail.water_supply_method" />
        <wd-cell title="加压分区情况" :value="detail.pressurized_partition_situation" />
        <wd-cell title="水泵数量(台)" :value="detail.sbsl" />
        <wd-cell title="水泵材质" :value="detail.sbcz" />
        <wd-cell title="水池/箱(座)" :value="detail.dxsc" />
        <wd-cell title="水池/箱材质" :value="detail.dxscxcz" />
        <wd-cell title="泵房管路材质" :value="detail.bfglcz" />
        <wd-cell title="泵房阀门主要材质" :value="detail.bffmzycz" />
        <wd-cell title="改造完成年代" :value="detail.eggzwcnd" />
      </wd-cell-group>
    </wd-collapse-item>
  </wd-collapse>
  <div class="back-white border-R14 box-shadow">
    <wd-status-tip image="search" v-if="!code" tip="无档案数据" />
  </div>
  <div style="padding: 80rpx"></div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { getDwd_gwyy_xqxttz } from '/src/services/model/material.js'
import uniUtil from '/src/utils/uniUtil.js'
const detail = ref()
const record = ref([])
const keys = { pipeDia: '市政管径', location: '水表组位置', pipe: '市政管材', status: '水表组阀门状态', waterStatus: '预留口阀门状态', value: '预留阀门口号', waterLocation: '预留口位置' }

const props = defineProps({ code: String })

watch(
  () => props.code,
  async (val) => {
    if (!val) return (detail.value = null)
    try {
      const [data] = await getDwd_gwyy_xqxttz({ xqmc: null, xqbm: val })
      record.value = []
      uni.setNavigationBarTitle({ title: data.xqmc })
      detail.value = data
    } catch (error) {
      uniUtil.showToast(error.message)
    }
  },
  { immediate: true, deep: true }
)

const isShow = computed(() => {
  switch (detail.value.pump_room_status ?? '#') {
    case '无需二供改造':
      return [1]
    case '查漏补缺':
      return [1]
    case '已立项未进场':
      return [1]
    case '已立项施工中(指临供状态)':
      return [1, 2]
    case '已通水':
      return [1, 2, 3]
    default:
      return []
  }
})

function handlerCount(val) {
  if (!val || val == '""') return '暂无数据'
  const list = val.split(';')
  return (
    '楼栋总数 ' +
    list.reduce((total, item) => {
      let match = item.match(/栋数：(\d+)栋/)
      if (match) {
        let count = parseInt(match[1], 10)
        return total + count
      } else {
        return total
      }
    }, 0)
  )
}

function handlerblock(val) {
  if (!val || val == '""') return []
  return val.split(';')
}

function handlerlist(value) {
  if (!value || value == '""') return ''
  if (/^[\[]/.test(value)) return JSON.parse(value).join('、')
  return value.replace(/"/g, '')
}

function handlerBoolean(value) {
  return value === true ? '是' : value === false ? '否' : value
}

function handlerdata(value) {
  if (!value || value == '""') return ''
  return /^"/.test(value) ? JSON.parse(value) : [value]
}
</script>

<style lang="less" scoped>
.collapse {
  :deep(.wd-collapse-item__header) {
    background-color: rgb(247, 206, 143);
    margin-bottom: 10rpx;
  }
  :deep(.wd-collapse-item__body) {
    padding: 0;
  }
}
</style>
