<template>
  <wd-search v-model="text" @search="handleSearch" placeholder="搜索小区" custom-class=" box-shadow  border-eee" />
  <wd-action-sheet v-model="show" :actions="actions" @select="(item) => emit('select', item)" />
</template>

<script setup>
import { ref } from 'vue'
import { getZoneDataLike } from '/src/services/model/map.js'
import uniUtil from '/src/utils/uniUtil.js'
const emit = defineEmits(['select'])
const show = ref(false)

const actions = ref([])
async function handleSearch({ value }) {
  try {
    if (!value) return uniUtil.showToast('输入不可为空')
    const res = await getZoneDataLike(value)
    const result = JSON.parse(res.data)
    if (!result.length) return uniUtil.showToast('未找到有效区块')
    show.value = true
    actions.value = result.map((i) => ({ name: i.Zone_Name, Zone_Code: i.Zone_Code, Center_Point: i.Center_Point.split(',') }))
  } catch (error) {
    uniUtil.showToast(error)
  }
}
</script>
