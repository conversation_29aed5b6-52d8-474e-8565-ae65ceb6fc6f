<template>
  <div class="all flex f-column">
    <wd-search @search="debounceHandlesearchChange" @change="debounceHandlesearchChange" />

    <div class="f-1"><MapGlS :variation @mapLoad="mapLoad" @mapClick="mapClick" /></div>
    <div class="absolute" style="left: 20rpx; bottom: 20rpx"><MapLegend :legend /></div>

    <ControlLayer @change="layerChange" :layerList style="bottom: 280rpx; right: 50rpx; z-index: 99" />
    <div class="absolute box-shadow f-xy-center pad-10 back-white border-R12" @click="addInformation" style="bottom: 190rpx; right: 50rpx"><wd-icon name="add" color="#2d2d2d" size="22px"></wd-icon></div>
    <div class="absolute box-shadow f-xy-center pad-10 back-white border-R12" @click="handleOneself" style="bottom: 100rpx; right: 50rpx"><image style="width: 44rpx; height: 44rpx" src="/static/DW.png"></image></div>

    <PopupMenu v-model="show">
      <template #menu>
        <div class="f-xy-center fon-W700 fon-S28">{{ detail.zone_Name }}</div>
      </template>
      <template #content> <PopupDetali :detail @clickBack="show = -1" /> </template>
    </PopupMenu>
  </div>

  <!-- 搜索列表弹窗 -->
  <wd-popup :modal="false" v-model="searchOpen" closable position="bottom" custom-style="min-height: 1000rpx; z-index:99">
    <div class="pad-24">
      <div class="pad-Y32"></div>
      <div class="flex f-between" style="background-color: #32cbdb">
        <div class="f-1 pad-16 text-center color-white">小区名称</div>
        <div class="f-1 pad-16 text-center color-white">小区编码</div>
      </div>
      <div class="overflow-auto" style="max-height: 800rpx">
        <template v-for="(item, index) in searchList" :key="index">
          <div class="item pad-16 mar-Y12 border-B-eee flex f-between" @click="searchItemClick(item)">
            <div class="f-1 text-center">{{ item.name }}</div>
            <div class="f-1 text-center">{{ item.Zone_Code }}</div>
          </div>
        </template>
        <wd-status-tip v-if="searchList.length == 0" image="search" tip="当前搜索无结果" />
      </div>
    </div>
  </wd-popup>
  <FeedbackPopup ref="feedbackRef" :id="9" />

  <wd-toast />
  <wd-message-box />
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { onShow, onNavigationBarButtonTap, onBackPress } from '@dcloudio/uni-app'

import MapGlS from './components/MapGlS/index.vue'
import PopupMenu from '/src/components/PopupMenu/index.vue'
import PopupDetali from './components/PopupDetali.vue'
import MapLegend from '/src/components/MapLegend/index.vue'
import debounce from 'lodash/debounce'
import { getZoneDataLike } from '/src/services/model/map.js'
import FeedbackPopup from '/src/components/FeedbackPopup/index.vue'

import ControlLayer from '/src/components/ControlLayer/index.vue'
import { getValveVerificationDetailApi } from '/src/services/model/submit.js'

import useMapDotStore from '/src/store/mapDot.js'
import { storeToRefs } from 'pinia'

import { useToast } from 'wot-design-uni'

const toast = useToast()

const { getFieldRecords } = useMapDotStore()
const { fieldRecords } = storeToRefs(useMapDotStore())
const variation = ref(null)

const show = ref(-1)

const feedbackRef = ref(null)

// 监听按钮点击事件
onNavigationBarButtonTap(() => feedbackRef.value.getFeedbackDetail())
onBackPress((e) => {
  const isShow = feedbackRef.value.getFeedbackIsOpen()
  if (show.value != -1 || searchOpen.value === true || isShow) {
    show.value = -1
    searchOpen.value = false
    feedbackRef.value.close()
    return true
  }
})

onShow(async () => {
  getFieldRecords()
  if (show.value !== -1) {
    const { data } = await getValveVerificationDetailApi(detail.value.id)
    detail.value = data.valveVerification[0]
  }
})

watch(fieldRecords, (value) => {
  setTimeout(() => (variation.value = { type: 'updatedDot', value }))
  setTimeout(() => (variation.value = { type: 'setFillColor', value }))
})

const detail = ref(null)

const legend = computed(() => {
  const obj = {
    总阀: { url: 'https://www.szwgft.cn:8090/AppIcon/icon1.png', value: 0, unit: '个' },
    总表: { url: 'https://www.szwgft.cn:8090/AppIcon/icon2.png', value: 0, unit: '个' },
    立管点: { url: 'https://www.szwgft.cn:8090/AppIcon/icon3.png', value: 0, unit: '个' }
  }

  for (let i = 0; i < fieldRecords.value.length; i++) {
    const item = fieldRecords.value[i]
    obj[item.properties.point_Type].value++
  }

  return Object.entries(obj).map(([key, value]) => ({ label: key, ...value }))
})

function mapLoad() {
  // 添加地图icon
  variation.value = {
    type: 'loadingImages',
    value: { 总阀: 'https://www.szwgft.cn:8090/AppIcon/icon1.png', 总表: 'https://www.szwgft.cn:8090/AppIcon/icon2.png', 立管点: 'https://www.szwgft.cn:8090/AppIcon/icon3.png' }
  }
}

async function mapClick(val) {
  detail.value = val
  searchOpen.value = false
  show.value = 0
}

async function handleOneself() {
  toast.loading('正在获取位置')
  const { latitude, longitude } = await uni.getLocation({ altitude: true })
  variation.value = { type: 'moveto', value: [longitude, latitude] }
  toast.close()
}

function addInformation() {
  variation.value = { type: 'addMapMarker', value: true }
}

const layerList = ref([
  { layerId: 'point', name: '总阀总表', checked: true },
  { layerId: ['feedwater-line', 'feedwater-symbol', 'feedwater-symbol2'], name: '供水管网', checked: true },
  { layerId: 'zone-fill2', name: '需核对小区' }
])

// 图层控制
function layerChange(e) {
  variation.value = { type: 'layerChange', value: e }
}

// 搜索
const searchList = ref([])
const searchOpen = ref(false)

const debounceHandlesearchChange = debounce(searchChange, 500)
async function searchChange({ value }) {
  if (!value) return
  const res = await getZoneDataLike(value)
  const result = JSON.parse(res.data)
  searchList.value = result.map((i) => ({ name: i.Zone_Name, Zone_Code: i.Zone_Code, Center_Point: i.Center_Point.split(',') }))
  searchOpen.value = true
}

// 搜索内容点击
function searchItemClick(item) {
  variation.value = { type: 'reach', value: item }
  searchOpen.value = false
}
</script>

<style lang="less" scoped>
:deep(.custom-button) {
  min-width: auto !important;
  box-sizing: border-box;
  width: 32px !important;
  height: 32px !important;
  border-radius: 16px !important;
  margin: 8rpx;
}

:deep(.custom-radio) {
  height: 32px !important;
  line-height: 32px !important;
}

.item {
  background-color: #eee;
}
.item:nth-child(2n) {
  background-color: antiquewhite;
}
</style>
