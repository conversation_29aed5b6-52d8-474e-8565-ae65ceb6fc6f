<template>
  <view class="map-box all">
    <view id="********************************" class="all"></view>
    <view :variation :change:variation="map.watchVariationChange" />
  </view>
</template>

<script>
import uniUtil from '/src/utils/uniUtil'

export default {
  props: ['variation'],
  methods: {
    mapClick(e) {
      this.$emit('pumpHouseClick', e)
    },
    markerClick(value) {
      const { Subdistric, code, lnglat, name, Grid, Belong_Are } = value
      uniUtil.navigateTo(`/src/pages/mine/children/pump-house/pumpHouseCreate?lngLat=${JSON.stringify(lnglat)}&code=${code}&name=${name}&Subdistric=${Subdistric}&Grid=${Grid}&Belong_Are=${Belong_Are}`)
    }
  }
}
</script>

<script module="map" lang="renderjs">
import mapboxgl from 'mapbox-gl'

import { mapConfig } from './mapConfig'
export default {
  data() {
    return { myMap: null }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    initMap() {
      mapboxgl.accessToken = mapConfig.accessToken
      const mapOptions = { ...mapConfig, container:'********************************' }
      this.myMap = new mapboxgl.Map(mapOptions)
      this.myMap.on('load', () => {
        this.loadingImages({
          异常: 'https://www.szwgft.cn/nodeServer/img/1.png',
          临供: 'https://www.szwgft.cn/nodeServer/img/2.png',
          切换: 'https://www.szwgft.cn/nodeServer/img/3.png',
          初验: 'https://www.szwgft.cn/nodeServer/img/4.png',
          已采集:'https://www.szwgft.cn/nodeServer/img/4.png',
          未采集:'https://www.szwgft.cn/nodeServer/img/2.png'
        })
      })

      this.addDotEvent()


    },
    loadingImages(imagesOptions) {
      const images = Object.entries(imagesOptions)
      images.forEach(([imageName, imageUrl]) => this.myMap.loadImage(imageUrl, (error, image) => this.myMap.addImage( imageName, image)))
    },

    // 事件触发接收器
    watchVariationChange(e){
      if(e){
      const { type, value } = e
        this[type](value)
      }
    },

    // 渲染泵房
    updatePumpHouseDots(pumpHouseDots) {
      if (this.myMap) {
        const pumpHouseOptions = { type: 'FeatureCollection', features: pumpHouseDots }
        this.myMap.getSource('dot-source').setData(pumpHouseOptions)
      }
    },

    moveto(center){
      this.myMap.flyTo({ center:center, zoom: 17.8, duration: 1500 })
    },
    cutCollect({isCollect}){
      const type = isCollect ? 'isCollect' :'state'
      this.myMap.setLayoutProperty('point', 'icon-image', ['get', type])
    },

    // 泵房添加点击事件
    addDotEvent(){
      this.myMap.on('click', 'point', (e) => {
        const [feature] = this.myMap.queryRenderedFeatures(e.point, { layers: ['point'] })
        const { properties } = feature
        this.$ownerInstance.callMethod('mapClick', properties)
      })
    },
    addMapMarker(){
      if (this.Marker) {
        this.Marker.remove()
        this.Marker = null
        this.myMap.off('move', this.handlerMoveEvent)
        return
      }else{
        this.handlerMoveEvent = () => { this.Marker.setLngLat(Object.values(this.myMap.getCenter())) }
        this.myMap.on('move', this.handlerMoveEvent)
        this.Marker = new mapboxgl.Marker().setLngLat(Object.values(this.myMap.getCenter())).addTo(this.myMap)

        this.handlerMarkerClick =(event) => {
        event.stopPropagation()
        this.myMap.off('move', this.handlerMoveEvent)
        // 查询区块要素
        const [feature] = this.myMap.queryRenderedFeatures([this.Marker._pos.x, this.Marker._pos.y], { layers: ['zone-fill']})
        console.log(feature);

        const { Zone_Code, Zone_Name, Subdistric, Grid, Belong_Are } = feature?.properties ?? { Zone_Code: '超出边界', Subdistric: '超出边界', Zone_Name: '超出边界' }
        const data = {code : Zone_Code, name: Zone_Name, Grid, Belong_Are, Subdistric,lnglat:this.Marker._lngLat}
        this.$ownerInstance.callMethod('markerClick', data)
        this.Marker.getElement().removeEventListener('click', this.handlerMarkerClick)
        this.Marker.remove()
        this.Marker = null
      }
        // 标记添加点击事件
        this.Marker.getElement().addEventListener('click', this.handlerMarkerClick)
      }
    },
    search(value){
      console.log(value);
      this.myMap.setFilter('tubeLine', ['==', 'FEATID', value])

    }
  }

}
</script>

<style lang="less" scoped>
.map-box {
}
</style>
