import { nodeRequest } from '../index'

class FieldWork {
  list(page, pageSize = 10) {
    if (page) {
      return nodeRequest.get('/nodeServer/fieldWork/list', { params: { page, pageSize } })
    } else {
      return nodeRequest.get('/nodeServer/fieldWork/list')
    }
  }
  search(zoneName, page) {
    return nodeRequest.post('/nodeServer/fieldWork/search', { zoneName, page, pageSize: 16 })
  }
}

export const FieldWorkApi = new FieldWork()
