<template>
  <div class="field-work-detail">
    <!-- 头部状态卡片 -->
    <div class="header-section">
      <div class="status-card">
        <div class="status-header">
          <div class="facility-info">
            <div class="facility-icon">
              <wd-icon :name="getPointTypeIcon(detail.point_Type)" size="24px" color="#fff"></wd-icon>
            </div>
            <div class="facility-details">
              <div class="facility-title">{{ detail.point_Type || '现场作业点' }}</div>
              <div class="facility-subtitle">{{ detail.zone_Name || '作业区域' }}</div>
            </div>
          </div>
          <div class="status-badge" :class="getStatusClass()">
            <div class="status-dot"></div>
            <div class="status-text">{{ getStatusText() }}</div>
          </div>
        </div>

        <!-- 关键指标 -->
        <div class="key-metrics">
          <div class="metric-item">
            <div class="metric-icon">
              <wd-icon name="ruler" size="16px" color="#1890ff"></wd-icon>
            </div>
            <div class="metric-content">
              <div class="metric-label">管径</div>
              <div class="metric-value">{{ detail.pipe_Diameter || '--' }} <span class="unit">mm</span></div>
            </div>
          </div>

          <div class="metric-item" v-if="detail.point_Type !== '立管点'">
            <div class="metric-icon">
              <wd-icon name="arrow-down" size="16px" color="#52c41a"></wd-icon>
            </div>
            <div class="metric-content">
              <div class="metric-label">埋深</div>
              <div class="metric-value">{{ detail.burial_Depth || '--' }} <span class="unit">m</span></div>
            </div>
          </div>

          <div class="metric-item" v-if="detail.point_Type !== '立管点'">
            <div class="metric-icon">
              <wd-icon name="arrow-up" size="16px" color="#fa8c16"></wd-icon>
            </div>
            <div class="metric-content">
              <div class="metric-label">管顶高度</div>
              <div class="metric-value">{{ detail.to_Distance || '--' }} <span class="unit">m</span></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细信息区域 -->
    <div class="content-section">
      <!-- 设施规格 -->
      <div class="info-group">
        <div class="group-header">
          <div class="group-icon">
            <wd-icon name="setting" size="18px" color="#4d63e0"></wd-icon>
          </div>
          <div class="group-title">设施规格</div>
        </div>

        <div class="spec-grid">
          <div class="spec-card primary-spec">
            <div class="spec-header">
              <wd-icon name="category" size="14px" color="#722ed1"></wd-icon>
              <span class="spec-label">点位类型</span>
            </div>
            <div class="spec-value point-type-tag" :class="getPointTypeClass(detail.point_Type)">
              {{ detail.point_Type || '--' }}
            </div>
          </div>

          <div class="spec-card" v-if="detail.point_Type !== '立管点'">
            <div class="spec-header">
              <wd-icon name="fire" size="14px" color="#ff4d4f"></wd-icon>
              <span class="spec-label">生消类型</span>
            </div>
            <div class="spec-value type-tag" :class="getTypeClass(detail.type)">
              {{ detail.type || '--' }}
            </div>
          </div>

          <div class="spec-card">
            <div class="spec-header">
              <wd-icon name="link" size="14px" color="#13c2c2"></wd-icon>
              <span class="spec-label">管材</span>
            </div>
            <div class="spec-value material-tag">
              {{ detail.pipe_Material || '--' }}
            </div>
          </div>

          <div class="spec-card" v-if="detail.point_Type !== '立管点'">
            <div class="spec-header">
              <wd-icon name="tool" size="14px" color="#fa541c"></wd-icon>
              <span class="spec-label">阀体材料</span>
            </div>
            <div class="spec-value material-tag">
              {{ detail.valve_Material || '--' }}
            </div>
          </div>

          <div class="spec-card" v-if="detail.point_Type !== '立管点'">
            <div class="spec-header">
              <wd-icon name="branches" size="14px" color="#52c41a"></wd-icon>
              <span class="spec-label">进水路数</span>
            </div>
            <div class="spec-value count-value">
              {{ detail.inlet_Waterways || '--' }}
            </div>
          </div>
        </div>

        <!-- 水表信息 -->
        <div class="meter-section" v-if="detail.point_Type !== '立管点' && (detail.water_Meter_Type || detail.water_Meter_Coding)">
          <div class="meter-header">
            <wd-icon name="chart-pie" size="16px" color="#1890ff"></wd-icon>
            <span class="meter-title">水表信息</span>
          </div>
          <div class="meter-details">
            <div class="meter-item" v-if="detail.water_Meter_Type">
              <span class="meter-label">类型</span>
              <span class="meter-value">{{ detail.water_Meter_Type }}</span>
            </div>
            <div class="meter-item" v-if="detail.water_Meter_Coding">
              <span class="meter-label">编码</span>
              <span class="meter-value">{{ detail.water_Meter_Coding }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 位置信息 -->
      <div class="info-group">
        <div class="group-header">
          <div class="group-icon">
            <wd-icon name="location" size="18px" color="#52c41a"></wd-icon>
          </div>
          <div class="group-title">位置信息</div>
        </div>

        <div class="location-card">
          <div class="location-header">
            <div class="location-icon">
              <wd-icon name="home" size="20px" color="#fff"></wd-icon>
            </div>
            <div class="location-details">
              <div class="location-title">{{ detail.zone_Name || '未知区域' }}</div>
              <div class="location-address">{{ detail.address || '地址信息待完善' }}</div>
            </div>
          </div>

          <div class="coordinates-section">
            <div class="coordinate-item">
              <div class="coordinate-header">
                <wd-icon name="compass" size="14px" color="#1890ff"></wd-icon>
                <span class="coordinate-label">经度 (X)</span>
              </div>
              <div class="coordinate-value">{{ detail.x || '--' }}</div>
            </div>

            <div class="coordinate-item">
              <div class="coordinate-header">
                <wd-icon name="compass" size="14px" color="#52c41a"></wd-icon>
                <span class="coordinate-label">纬度 (Y)</span>
              </div>
              <div class="coordinate-value">{{ detail.y || '--' }}</div>
            </div>
          </div>

          <div class="location-status">
            <div class="status-indicator" :class="detail.x && detail.y ? 'status-active' : 'status-inactive'">
              <div class="status-dot"></div>
              <span class="status-text">{{ detail.x && detail.y ? '坐标信息完整' : '坐标信息缺失' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 现场信息 -->
      <div class="info-group">
        <div class="group-header">
          <div class="group-icon">
            <wd-icon name="camera" size="18px" color="#fa8c16"></wd-icon>
          </div>
          <div class="group-title">现场信息</div>
        </div>

        <div class="site-card">
          <!-- 现场照片 -->
          <div class="photo-section" v-if="detail.site_Photo">
            <div class="photo-header">
              <wd-icon name="camera" size="16px" color="#fa8c16"></wd-icon>
              <span class="photo-title">现场照片</span>
            </div>
            <div class="photo-gallery">
              <UploadImage cancel class="mr-t24" disabled :maxlength="1" v-model="detail.site_Photo" />
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="notes-section" :class="{ 'mar-T20': detail.site_Photo }">
            <div class="notes-header">
              <wd-icon name="edit" size="16px" color="#722ed1"></wd-icon>
              <span class="notes-title">备注信息</span>
            </div>
            <div class="notes-content">
              <div class="notes-text" v-if="detail.notes">{{ detail.notes }}</div>
              <div class="notes-empty" v-else>暂无备注信息</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作信息 -->
      <div class="info-group">
        <div class="group-header">
          <div class="group-icon">
            <wd-icon name="user" size="18px" color="#722ed1"></wd-icon>
          </div>
          <div class="group-title">操作信息</div>
        </div>

        <div class="operation-cards">
          <div class="operation-card">
            <div class="operation-header">
              <wd-icon name="add-circle" size="16px" color="#52c41a"></wd-icon>
              <span class="operation-title">创建信息</span>
            </div>
            <div class="operation-details">
              <div class="operation-item">
                <span class="operation-label">创建人员</span>
                <span class="operation-value">{{ detail.uploader || '--' }}</span>
              </div>
              <div class="operation-item">
                <span class="operation-label">创建时间</span>
                <span class="operation-value">{{ formatDateTime(detail.created_Time) }}</span>
              </div>
            </div>
          </div>

          <div class="operation-card" v-if="detail.updater">
            <div class="operation-header">
              <wd-icon name="edit" size="16px" color="#1890ff"></wd-icon>
              <span class="operation-title">更新信息</span>
            </div>
            <div class="operation-details">
              <div class="operation-item">
                <span class="operation-label">更新人员</span>
                <span class="operation-value">{{ detail.updater || '--' }}</span>
              </div>
              <div class="operation-item">
                <span class="operation-label">更新时间</span>
                <span class="operation-value">{{ formatDateTime(detail.updated_Time) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section" v-if="detail.updater === uniUtil.get('userInfo').name">
      <div class="action-buttons">
        <button class="action-btn delete-btn" @click="handleDelete">
          <wd-icon name="delete" size="18px"></wd-icon>
          <span>删除记录</span>
        </button>
        <button class="action-btn edit-btn" @click="amendClick">
          <wd-icon name="edit" size="18px"></wd-icon>
          <span>编辑信息</span>
        </button>
      </div>
    </div>
  </div>
  <wd-message-box />
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { deleteValveVerificationApi, getValveVerificationDetailApi } from '/src/services/model/submit.js'
import UploadImage from '/src/components/UploadImage/index.vue'
import uniUtil from '/src/utils/uniUtil'
import { useMessage } from 'wot-design-uni'
import { CommonApi } from '/src/services/model/common.js'

let id = null
const selectOption = ref({})

onLoad(async (e) => {
  id = e.id
  const { data: fieldWorkFacility } = await CommonApi.queryDictionaries('fieldWorkFacility') //外业点子类
  selectOption.value.fieldWorkFacility = fieldWorkFacility
  const { data: fieldWorkFireControl } = await CommonApi.queryDictionaries('fieldWorkFireControl') //外业生消类型
  selectOption.value.fieldWorkFireControl = fieldWorkFireControl
  const { data: fieldWorkPipelineMaterial } = await CommonApi.queryDictionaries('fieldWorkPipelineMaterial') //外业管材材料类型
  selectOption.value.fieldWorkPipelineMaterial = fieldWorkPipelineMaterial.map((item) => ({ label: item.DictValue, value: item.DictValue }))
  const { data: fieldWorkValveBodyMateria } = await CommonApi.queryDictionaries('fieldWorkValveBodyMateria') //外业阀体材料类型
  selectOption.value.fieldWorkValveBodyMateria = fieldWorkValveBodyMateria.map((item) => ({ label: item.DictValue, value: item.DictValue }))
})

onShow(async () => {
  try {
    const { data } = await getValveVerificationDetailApi(id)
    detail.value = data.valveVerification[0]
  } catch (error) {
    uniUtil.showToast(error)
  }
})

const detail = ref({})

// 获取点位类型图标
function getPointTypeIcon(pointType) {
  const iconMap = {
    总阀: 'setting',
    总表: 'chart',
    阀门: 'setting',
    水表: 'chart-pie',
    管道: 'link',
    泵房: 'home',
    设备: 'tool',
    监测点: 'location',
    立管点: 'link'
  }
  return iconMap[pointType] || 'location'
}

// 获取状态样式类
function getStatusClass() {
  if (detail.value.x && detail.value.y && detail.value.site_Photo) {
    return 'status-complete'
  } else if (detail.value.x && detail.value.y) {
    return 'status-partial'
  }
  return 'status-incomplete'
}

// 获取状态文本
function getStatusText() {
  if (detail.value.x && detail.value.y && detail.value.site_Photo) {
    return '信息完整'
  } else if (detail.value.x && detail.value.y) {
    return '部分完整'
  }
  return '信息不完整'
}

// 获取点位类型样式类
function getPointTypeClass(pointType) {
  const classMap = {
    总阀: 'type-valve',
    总表: 'type-meter',
    阀门: 'type-valve',
    水表: 'type-meter',
    管道: 'type-pipe',
    泵房: 'type-pump',
    设备: 'type-device',
    监测点: 'type-monitor',
    立管点: 'type-riser'
  }
  return classMap[pointType] || 'type-default'
}

// 获取生消类型样式类
function getTypeClass(type) {
  const classMap = {
    生活: 'fire-life',
    消防: 'fire-safety',
    '生活+消防': 'fire-both'
  }
  return classMap[type] || 'fire-default'
}

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '--'
  const date = new Date(dateTime)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}/${month}/${day} ${hours}:${minutes}`
}

function amendClick() {
  uniUtil.navigateTo(`/src/pages/mine/children/field-work/updata?id=${id}`)
}

const message = useMessage()

function handleDelete() {
  message.confirm({ msg: '确定删除该条数据？', title: '删除' }).then(async () => {
    const { code } = await deleteValveVerificationApi(id)
    if (code != 200) return uniUtil.showToast('删除失败！')
    uniUtil.showToast('删除成功！')
    setTimeout(() => uniUtil.navigateBack(), 2000)
  })
}
</script>

<style scoped lang="less">
.field-work-detail {
  background: #f5f5f5;
  min-height: 100vh;
}

// 头部区域
.header-section {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.facility-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.facility-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(77, 99, 224, 0.3);
}

.facility-details {
  flex: 1;
}

.facility-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
}

.facility-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
  line-height: 1.3;
}

.status-badge {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;

  &.status-complete {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    border: 1rpx solid #b7eb8f;

    .status-dot {
      background: #52c41a;
      box-shadow: 0 0 0 3rpx rgba(82, 196, 26, 0.2);
    }

    .status-text {
      color: #52c41a;
    }
  }

  &.status-partial {
    background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
    border: 1rpx solid #ffcc02;

    .status-dot {
      background: #fa8c16;
      box-shadow: 0 0 0 3rpx rgba(250, 140, 22, 0.2);
    }

    .status-text {
      color: #fa8c16;
    }
  }

  &.status-incomplete {
    background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
    border: 1rpx solid #ffa39e;

    .status-dot {
      background: #ff4d4f;
      box-shadow: 0 0 0 3rpx rgba(255, 77, 79, 0.2);
    }

    .status-text {
      color: #ff4d4f;
    }
  }
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.status-text {
  font-size: 22rpx;
  font-weight: 600;
  line-height: 1.2;
}

// 关键指标
.key-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));
  gap: 16rpx;
}

.metric-item {
  display: flex;
  align-items: center;
  background: rgba(77, 99, 224, 0.04);
  padding: 12rpx 16rpx;
  border-radius: 16rpx;
  border-left: 3rpx solid #4d63e0;
}

.metric-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  background: rgba(77, 99, 224, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 20rpx;
  color: #666;
  line-height: 1.2;
  margin-bottom: 2rpx;
}

.metric-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;

  .unit {
    font-size: 18rpx;
    font-weight: 400;
    color: #999;
    margin-left: 2rpx;
  }
}

// 内容区域
.content-section {
  padding: 20rpx;
}

.info-group {
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
}

.group-icon {
  width: 36rpx;
  height: 36rpx;
  border-radius: 10rpx;
  background: rgba(77, 99, 224, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

// 规格网格
.spec-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280rpx, 1fr));
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.spec-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #e8e8e8;
  transition: all 0.3s ease;

  &.primary-spec {
    border-left-color: #4d63e0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  }
}

.spec-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.spec-label {
  font-size: 22rpx;
  color: #666;
  margin-left: 8rpx;
  line-height: 1.2;
}

.spec-value {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.2;

  &.point-type-tag {
    display: inline-block;
    padding: 6rpx 16rpx;
    border-radius: 16rpx;
    font-size: 22rpx;
    font-weight: 600;

    &.type-valve {
      background: linear-gradient(135deg, #fff0f6 0%, #ffd6e7 100%);
      color: #eb2f96;
      border: 1rpx solid #ffadd2;
    }

    &.type-meter {
      background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
      color: #1890ff;
      border: 1rpx solid #91d5ff;
    }

    &.type-pipe {
      background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
      color: #52c41a;
      border: 1rpx solid #b7eb8f;
    }

    &.type-pump {
      background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
      color: #722ed1;
      border: 1rpx solid #d3adf7;
    }

    &.type-device {
      background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
      color: #fa8c16;
      border: 1rpx solid #ffcc02;
    }

    &.type-monitor {
      background: linear-gradient(135deg, #e6fffb 0%, #b5f5ec 100%);
      color: #13c2c2;
      border: 1rpx solid #87e8de;
    }

    &.type-riser {
      background: linear-gradient(135deg, #fff2e8 0%, #ffd591 100%);
      color: #fa541c;
      border: 1rpx solid #ffbb96;
    }

    &.type-default {
      background: linear-gradient(135deg, #f5f5f5 0%, #d9d9d9 100%);
      color: #666;
      border: 1rpx solid #d9d9d9;
    }
  }

  &.type-tag {
    display: inline-block;
    padding: 6rpx 16rpx;
    border-radius: 16rpx;
    font-size: 22rpx;
    font-weight: 600;

    &.fire-life {
      background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
      color: #52c41a;
      border: 1rpx solid #b7eb8f;
    }

    &.fire-safety {
      background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
      color: #ff4d4f;
      border: 1rpx solid #ffa39e;
    }

    &.fire-both {
      background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
      color: #fa8c16;
      border: 1rpx solid #ffcc02;
    }

    &.fire-default {
      background: linear-gradient(135deg, #f5f5f5 0%, #d9d9d9 100%);
      color: #666;
      border: 1rpx solid #d9d9d9;
    }
  }

  &.material-tag {
    color: #13c2c2;
    font-weight: 600;
  }

  &.count-value {
    color: #52c41a;
    font-weight: 600;
  }
}

// 水表信息
.meter-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #1890ff;
}

.meter-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.meter-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-left: 8rpx;
  line-height: 1.2;
}

.meter-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 12rpx;
}

.meter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(24, 144, 255, 0.04);
  padding: 12rpx 16rpx;
  border-radius: 12rpx;
}

.meter-label {
  font-size: 22rpx;
  color: #666;
  line-height: 1.2;
}

.meter-value {
  font-size: 24rpx;
  font-weight: 500;
  color: #1890ff;
  line-height: 1.2;
}

// 位置信息
.location-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #52c41a;
}

.location-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.location-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
}

.location-details {
  flex: 1;
}

.location-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.location-address {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
  line-height: 1.3;
}

.coordinates-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.coordinate-item {
  background: rgba(82, 196, 26, 0.04);
  padding: 16rpx;
  border-radius: 12rpx;
  border-left: 3rpx solid #52c41a;
}

.coordinate-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.coordinate-label {
  font-size: 22rpx;
  color: #666;
  margin-left: 8rpx;
  line-height: 1.2;
}

.coordinate-value {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
  word-break: break-all;
}

.location-status {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

.status-indicator {
  display: flex;
  align-items: center;

  &.status-active {
    .status-dot {
      background: #52c41a;
      box-shadow: 0 0 0 3rpx rgba(82, 196, 26, 0.2);
    }

    .status-text {
      color: #52c41a;
    }
  }

  &.status-inactive {
    .status-dot {
      background: #d9d9d9;
    }

    .status-text {
      color: #999;
    }
  }
}

.status-text {
  font-size: 22rpx;
  line-height: 1.3;
  margin-left: 8rpx;
}

// 现场信息
.site-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #fa8c16;
}

.photo-section {
  .photo-header {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .photo-title {
    font-size: 24rpx;
    font-weight: 600;
    color: #333;
    margin-left: 8rpx;
    line-height: 1.2;
  }

  .photo-gallery {
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  }
}

.notes-section {
  .notes-header {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
  }

  .notes-title {
    font-size: 24rpx;
    font-weight: 600;
    color: #333;
    margin-left: 8rpx;
    line-height: 1.2;
  }

  .notes-content {
    background: rgba(250, 140, 22, 0.04);
    padding: 16rpx 20rpx;
    border-radius: 12rpx;
    border-left: 3rpx solid #fa8c16;
  }

  .notes-text {
    font-size: 26rpx;
    color: #333;
    line-height: 1.6;
    word-break: break-word;
  }

  .notes-empty {
    font-size: 24rpx;
    color: #999;
    line-height: 1.4;
    font-style: italic;
  }
}

// 操作信息
.operation-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: 16rpx;
}

.operation-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #722ed1;
}

.operation-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.operation-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-left: 8rpx;
  line-height: 1.2;
}

.operation-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.operation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operation-label {
  font-size: 22rpx;
  color: #666;
  line-height: 1.2;
}

.operation-value {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
}

// 操作按钮
.action-section {
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 26rpx;
  font-weight: 600;
  line-height: 1.2;
  transition: all 0.3s ease;
  cursor: pointer;

  span {
    margin-left: 8rpx;
  }

  &.delete-btn {
    background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
    color: #ff4d4f;
    border: 1rpx solid #ffa39e;

    &:active {
      background: linear-gradient(135deg, #ffccc7 0%, #ffa39e 100%);
      transform: scale(0.98);
    }
  }

  &.edit-btn {
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
    color: #1890ff;
    border: 1rpx solid #91d5ff;

    &:active {
      background: linear-gradient(135deg, #bae7ff 0%, #91d5ff 100%);
      transform: scale(0.98);
    }
  }
}

// 通用样式
.mar-T20 {
  margin-top: 20rpx;
}

// 响应式设计
@media (max-width: 750rpx) {
  .key-metrics {
    grid-template-columns: 1fr;
  }

  .spec-grid {
    grid-template-columns: 1fr;
  }

  .coordinates-section {
    grid-template-columns: 1fr;
  }

  .meter-details {
    grid-template-columns: 1fr;
  }

  .operation-cards {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }
}

// 动画效果
.spec-card,
.location-card,
.site-card,
.operation-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .field-work-detail {
    background: #1a1a1a;
  }

  .status-card,
  .spec-card,
  .location-card,
  .site-card,
  .operation-card {
    background: #2a2a2a;
    color: #ffffff;
  }

  .facility-title,
  .group-title,
  .spec-value,
  .location-title,
  .notes-text,
  .operation-value {
    color: #ffffff;
  }

  .facility-subtitle,
  .spec-label,
  .location-address,
  .coordinate-label,
  .notes-empty,
  .operation-label {
    color: #cccccc;
  }
}
</style>
