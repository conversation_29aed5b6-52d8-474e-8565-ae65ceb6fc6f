<template>
  <view class="all" id="DOMID"></view>
  <view :center :data :change:data="map.updatedDot" :change:center="map.reach"></view>
</template>

<script setup>
import { ref } from 'vue'

defineProps({ data: Array })
const center = ref(null)
const reach = (val) => (center.value = val)

defineExpose({ reach })
</script>

<script>
import { getZoneData } from '/src/services/model/map.js'
import uniUtil from '/src/utils/uniUtil'
export default {
  methods: {
    async mapClick(value) {
      try {
        const result = await getZoneData(value.Zone_Code)
        const [res] = JSON.parse(result.data)
        this.$emit('mapClick', res)
      } catch (error) {
        uniUtil.showToast(error)
      }
    },
    mapdotClick(val) {
      uniUtil.navigateTo(val.path)
    }
  }
}
</script>

<script module="map" lang="renderjs">
import mapboxgl from 'mapbox-gl'
import basicsConfig from './config.js'

export default {
  data(){
    return{
      Map: null,
    }
  },
  mounted() {
    this.initMap()
  },
  beforeUnmount(){
    // 销毁地图实例
    this.Map?.remove()
    this.Map = null
  },
  methods: {
    // 初始化地图
    initMap(options = {}) {
      if(this.Map) return
      const accessToken = options?.accessToken ?? basicsConfig.map.accessToken
      const mapConfig = options?.map ?? {}
      mapboxgl.accessToken = accessToken
      const mapOptions = { container: 'DOMID', ...basicsConfig.map, ...mapConfig }
      this.Map = new mapboxgl.Map(mapOptions)
      this.Map.on('load', () => {
        this.loadingImages({
          总阀: 'https://www.szwgft.cn:8090/AppIcon/icon1.png',
          总表: 'https://www.szwgft.cn:8090/AppIcon/icon2.png',
          已处理: 'https://www.szwgft.cn:8090/AppIcon/1.png',
          未处理: 'https://www.szwgft.cn:8090/AppIcon/0.png',
          异常: 'https://www.szwgft.cn:8090/AppIcon/marker0.png',
          临供: 'https://www.szwgft.cn:8090/AppIcon/marker1.png',
          切换: 'https://www.szwgft.cn:8090/AppIcon/marker2.png',
          初验: 'https://www.szwgft.cn:8090/AppIcon/marker3.png'
        })
        this.addDotEvent()

      })



      this.addLayerEvent()

    },
    loadingImages(imagesOptions) {
      const images = Object.entries(imagesOptions)
      images.forEach(([imageName, imageUrl]) => this.Map.loadImage(imageUrl, (error, image) => this.Map.addImage( imageName, image)))
    },
    // 更新数据
    updatedDot(data) {
      // if (!data.length) return
      const source = this.Map.getSource('dot-source')
      if(!source) return
      const updatedGeojsonData = { type: 'FeatureCollection', features: data }
      source.setData(updatedGeojsonData)
    },
    reach(data) {
      if (!data) return
      this.Map.flyTo({ center: data.Center_Point, zoom: 15, curve: 1.5, duration: 2500 })
      this.Map.setFilter('zone-line2', ['match', ['get', 'Zone_Name'], data.name, true, false])
      // this.Map.setPaintProperty('zone_fill-extrusion', 'fill-extrusion-color', ['match', ['get', 'Zone_Code'], data.Zone_Code, '#7fb192', '#fff'])
    },

    addLayerEvent(){
      this.Map.on('click', 'zone-fill', (e) => {
        this.Map.getCanvas().style.cursor = 'pointer'
        const features = this.Map.queryRenderedFeatures(e.point, { layers: ['zone-fill'] })
        if (!features.length) return
        const feature = features[0]
        const { Zone_Name, ManagerNam, Zone_Code } = feature.properties
        this.$ownerInstance.callMethod('mapClick', feature.properties)
        this.Map.setPaintProperty('zone_fill-extrusion', 'fill-extrusion-color', ['match', ['get', 'Zone_Code'], Zone_Code, '#7fb192', '#fff'])
        const [modelFeature] = this.Map.querySourceFeatures('houseModel', { sourceLayer: 'futianjianzhu',  filter: ['==', 'Zone_Code', Zone_Code]  });

        if (modelFeature) {
          const [x,y] = modelFeature.geometry.coordinates[0][0]
          this.Map.flyTo({ center: [x, Number(y) - 0.0025], zoom: 15, curve: 1.5, duration: 2500 })

        }
      })
    },
    //小区档案图层点击事件处理函数
    addDotEvent(){
      this.Map.on('click', 'point', (e) => {
        const [feature] = this.Map.queryRenderedFeatures(e.point, { layers: ['point'] })
        const { properties } = feature
        this.$ownerInstance.callMethod('mapdotClick', properties)
      })
    }
  }
}
</script>
