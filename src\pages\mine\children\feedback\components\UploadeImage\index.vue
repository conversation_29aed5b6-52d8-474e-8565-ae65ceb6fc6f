<template>
  <canvas class="canvas" canvas-id="mycanvas" id="mycanvas"> </canvas>
  <div class="" @click="uploadFile"><slot></slot></div>
  <wd-toast />
</template>

<script setup>
import { getCurrentInstance } from 'vue'
import { cache } from '/src/utils/cache'
import { useToast } from 'wot-design-uni'

const instance = getCurrentInstance()
const emit = defineEmits(['success'])
const props = defineProps({
  url: { type: String, default: '' },
  header: { type: Object, default: () => ({}) },
  quality: { type: Number, default: 0.3 }
})

const toast = useToast()

async function uploadFile() {
  try {
    const chooseImageRes = await uni.chooseImage({ count: 1 })
    toast.loading('上传中')
    const { width, height, path } = await uni.getImageInfo({ src: chooseImageRes.tempFilePaths[0] })
    const ctx = uni.createCanvasContext('mycanvas', instance)
    ctx.drawImage(path, 0, 0, width, height)
    ctx.draw()
    const { tempFilePath } = await canvasToTempFilePath(width, height, props.quality)
    const token = cache.get('userInfo').token
    const header = { Authorization: token ? `Bearer ${token}` : '', ...props.header }
    const { data } = await uni.uploadFile({ url: props.url, filePath: tempFilePath, header, name: 'file' })
    const res = JSON.parse(data)
    emit('success', res)
  } catch (error) {
    console.log(error)
  }
  toast.close()
}

function canvasToTempFilePath(width, height, quality = 0.3) {
  return new Promise((resovle) => {
    setTimeout(async () => {
      const res = await uni.canvasToTempFilePath({ width, height, fileType: 'jpg', canvasId: 'mycanvas', quality }, instance)
      resovle(res)
    }, 300)
  })
}
</script>

<style lang="less" scoped>
.canvas {
  width: 2000px;
  height: 2000px;
  position: absolute;
  top: -9999px;
  left: -9999px;
}
</style>
