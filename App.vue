<script setup>
import { onShow, onLaunch } from '@dcloudio/uni-app'
import { checkVersion } from '/src/services/model/map.js'
import uniUtil from './src/utils/uniUtil.js'
import { useCommonStore } from '/src/store/common.js'
import { storeToRefs } from 'pinia'

// #ifdef APP-PLUS
import { showDialog } from '@/uni_modules/leruge-app-update/js_sdk/leruge-app-update.js'
// #endif

const { getUserInformation } = useCommonStore()
const { screenInformation } = storeToRefs(useCommonStore())

onLaunch(async () => {
  getUserInformation()
  const userInfo = uniUtil.get('userInfo')
  // 未登录跳转登录页
  if (!userInfo) {
    uni.navigateTo({
      url: '/src/pages/login/index',
      // #ifdef APP-PLUS
      success: () => plus.navigator.closeSplashscreen()
      // #endif
    })
  } else {
    uni.switchTab({
      url: '/src/pages/home/<USER>',
      // #ifdef APP-PLUS
      success: () => plus.navigator.closeSplashscreen()
      // #endif
    })
  }
})
// #ifdef APP-PLUS
onShow(() => checkUpdate())
// #endif

// 版本更新检查
async function checkUpdate() {
  try {
    const userInfo = uniUtil.get('userInfo')
    const { appWgtVersion } = screenInformation.value
    const reslut = await checkVersion(appWgtVersion)
    const { renewable, data } = reslut
    if (!renewable) return //不允许更新或不在更新时间内
    if (data.test) {
      if (userInfo.name == '管理员') showDialog(data)
    } else {
      showDialog(data)
    }
  } catch (error) {
    uniUtil.showToast('版本更新检查失败' + error)
  }
}
</script>

<style>
view,
text {
  box-sizing: border-box;
  font-size: 28rpx;
}

page {
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}
</style>
