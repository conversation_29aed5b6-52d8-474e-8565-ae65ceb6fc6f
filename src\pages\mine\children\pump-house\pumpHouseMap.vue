<template>
  <div class="all flex f-column">
    <wd-search v-model="seekText" @search="debounceHandlesearchChange" @change="debounceHandlesearchChange">
      <template #prefix>
        <wd-popover mode="menu" :content="menu" @menuclick="changeSearchType">
          <view class="search-type">
            <text>{{ searchType }}</text>
            <wd-icon custom-class="icon-arrow" name="fill-arrow-down"></wd-icon>
          </view>
        </wd-popover>
      </template>
    </wd-search>
    <MapBox class="f-1" :variation @pumpHouseClick="handlePumpHouseClick" />

    <div v-if="userInfo?.Jurisdiction == '二供新增'" class="absolute box-shadow f-xy-center pad-10 back-white border-R12 z-index-99" @click="addInformation" style="bottom: 200rpx; right: 50rpx">
      <wd-icon name="add" color="#2d2d2d" size="24px"></wd-icon>
    </div>
    <div class="absolute box-shadow f-xy-center pad-10 back-white border-R12 z-index-99" @click="handleOneself" style="bottom: 100rpx; right: 50rpx"><image style="width: 50rpx; height: 50rpx" src="/static/DW.png"></image></div>

    <div class="map-legend box-shadow absolute z-index-99 back-white">
      <!-- 图例头部 - 参照info-section样式 -->
      <div class="legend-header" @click="toggleLegend">
        <div class="legend-title">
          <wd-icon name="list" size="16px" color="#4A90E2"></wd-icon>
          <span class="title-text">图例</span>
          <span class="legend-count">({{ Object.keys(legend[isCollect ? '采集' : '节点']).length }})</span>
        </div>
        <div class="legend-toggle">
          <wd-icon :name="isLegendCollapsed ? 'arrow-down' : 'arrow-up'" size="14px" color="#666"></wd-icon>
        </div>
      </div>

      <!-- 图例内容 -->
      <div class="legend-content" :class="{ collapsed: isLegendCollapsed }">
        <!-- 位置采集开关 - 参照section-title样式 -->
        <div class="collect-section">
          <wd-checkbox v-model="isCollect" shape="square">
            <div class="collect-label">
              <wd-icon name="location" size="14px" color="#50C878"></wd-icon>
              <span>位置采集</span>
            </div>
          </wd-checkbox>
        </div>

        <!-- 图例项目列表 - 参照info-row样式 -->
        <div class="legend-items">
          <template v-for="[key, value] in Object.entries(legend[isCollect ? '采集' : '节点'])" :key="key">
            <div class="legend-item">
              <image class="legend-icon" :src="value.url"></image>
              <span class="legend-name">{{ key }}</span>
              <span class="legend-value">{{ value.value }}</span>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>

  <!-- 泵房详情信息内容 -->
  <wd-floating-panel custom-class="floating-panel_c relative" v-model:height="height" :anchors="anchors">
    <div v-if="!isCollect" class="pad-X24">
      <div>
        <wd-icon custom-class="absolute close_icon" @click="height = 0" name="close" size="18px"></wd-icon>

        <!-- 优化的标题头部 -->
        <div class="pump-house-header">
          <div class="pump-house-title">{{ detail?.PumpHouseName }}</div>
          <div class="status-info">
            <div class="status-badge" :class="getStatusClass(detail?.RemouldState)">
              {{ detail?.RemouldState }}
            </div>
            <div class="batch-info">{{ batchKeys[detail?.Batch] }}</div>
          </div>
        </div>

        <!-- 基础信息 -->
        <div class="info-section" v-if="detail">
          <div class="section-title">
            <wd-icon name="home" size="16px" color="#4A90E2"></wd-icon>
            <span>基础信息</span>
          </div>
          <div class="info-content">
            <div class="info-row">
              <div class="info-item">
                <span class="label">改造状态</span>
                <span class="value" :class="[getStatusClass(detail.RemouldState), { 'empty-value': isEmpty(formatValue(detail.RemouldState)) }]">{{ formatValue(detail.RemouldState) }}</span>
              </div>
              <div class="info-item">
                <span class="label">泵房批次</span>
                <span class="value" :class="{ 'empty-value': isEmpty(formatValue(batchKeys[detail.Batch])) }">{{ formatValue(batchKeys[detail.Batch]) }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">加压供水户数</span>
                <span class="value highlight">{{ formatValue(detail.PressurizedHouseholds, '户') }}</span>
              </div>
              <div class="info-item">
                <span class="label">小区建设时间</span>
                <span class="value">{{ formatValue(detail.ConstructionTime) }}</span>
              </div>
            </div>
            <div class="info-row full">
              <div class="info-item">
                <span class="label">泵房运营管理状态</span>
                <span class="value">{{ formatValue(detail.PumpRoomControlledState) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 物业信息 -->
        <div class="info-section" v-if="detail">
          <div class="section-title">
            <wd-icon name="user" size="16px" color="#50C878"></wd-icon>
            <span>物业信息</span>
          </div>
          <div class="info-content">
            <div class="info-row">
              <div class="info-item">
                <span class="label">物业单位</span>
                <span class="value">{{ formatValue(detail.PropertyUnit) }}</span>
              </div>
              <div class="info-item">
                <span class="label">物业联系人</span>
                <span class="value">{{ formatValue(detail.ContactPerson) }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">物业电话</span>
                <span class="value phone-number">{{ formatValue(detail.PhoneNumber) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目信息 -->
        <div class="info-section" v-if="detail">
          <div class="section-title">
            <wd-icon name="setting" size="16px" color="#FF6B6B"></wd-icon>
            <span>项目信息</span>
          </div>
          <div class="info-content">
            <div class="info-row">
              <div class="info-item">
                <span class="label">项目进展状态</span>
                <span class="value" :class="getProgressClass(detail.ProgressStatus)">{{ formatValue(detail.ProgressStatus) }}</span>
              </div>
              <div class="info-item">
                <span class="label">运营管理状态</span>
                <span class="value">{{ formatValue(detail.OperationManagementState) }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">施工单位</span>
                <span class="value">{{ formatValue(detail.ConstructionUnit) }}</span>
              </div>
              <div class="info-item important">
                <span class="label">
                  <wd-icon name="warn-bold" size="12px" color="#FF6B6B"></wd-icon>
                  现场监管责任人
                </span>
                <span class="value">{{ formatValue(detail.PersonInCharge) }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">临供停水事件数</span>
                <span class="value" :class="detail.TemporarySupplyEvents > 0 ? 'warning' : 'success'">{{ formatValue(detail.TemporarySupplyEvents, '次') }}</span>
              </div>
              <div class="info-item">
                <span class="label">初步验收时间</span>
                <span class="value">{{ formatValue(detail.AcceptanceTime) }}</span>
              </div>
            </div>
            <div class="info-row full">
              <div class="info-item">
                <span class="label">备注</span>
                <span class="value remark">{{ formatValue(detail.Remark) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 位置信息 -->
        <div class="info-section" v-if="detail">
          <div class="section-title">
            <wd-icon name="location" size="16px" color="#9B59B6"></wd-icon>
            <span>位置信息</span>
          </div>
          <div class="info-content">
            <div class="coordinate-display">
              <span class="coordinate-label">坐标位置</span>
              <span class="coordinate-value" :class="{ 'empty-value': isEmpty(formatCoordinate(detail.X, detail.Y)) }">{{ formatCoordinate(detail.X, detail.Y) }}</span>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">所属街道</span>
                <span class="value">{{ formatValue(detail.BelongingStreet) }}</span>
              </div>
              <div class="info-item">
                <span class="label">所属片区</span>
                <span class="value">{{ formatValue(detail.BelongingArea) }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">所属网格</span>
                <span class="value">{{ formatValue(detail.Gridding) }}</span>
              </div>
            </div>
            <div class="info-row full">
              <div class="info-item">
                <span class="label">小区地址</span>
                <span class="value">{{ formatValue(detail.ResidentialAddress) }}</span>
              </div>
            </div>
            <div class="info-row full">
              <div class="info-item">
                <span class="label">泵房精确位置</span>
                <span class="value">{{ formatValue(detail.AccuratePosition) }}</span>
              </div>
            </div>

            <!-- 泵房图片 -->
            <div class="image-section" v-if="detail.PumpHouseImg">
              <div class="image-title">
                <wd-icon name="camera" size="16px" color="#9B59B6"></wd-icon>
                <span>泵房图片</span>
              </div>
              <div class="image-grid">
                <template v-for="item in (detail?.PumpHouseImg ?? '').split(',')" :key="item">
                  <div class="image-item" v-if="item" @click="previewImage(item)">
                    <image class="pump-image" :src="item.replace('http:', 'https:')"></image>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>

        <!-- 节点信息 -->
        <div class="info-section" v-if="detail">
          <div class="section-title">
            <wd-icon name="list" size="16px" color="#E67E22"></wd-icon>
            <span>节点信息</span>
          </div>
          <div class="info-content">
            <wd-tabs v-model="CurrentNode" custom-class="node-tabs">
              <block v-for="(item, index) in pumpHouseNodeKeys" :key="item">
                <wd-tab :title="`${item}`">
                  <div class="node-content">
                    <div v-if="pumpHouseNodeDetail" class="node-detail">
                      <div class="node-status text-center">
                        <span class="status-label">{{ index + 1 }}_{{ item }}_</span>
                        <span :style="`color:  ${pumpHouseNodeDetail.IsEnd ? '#13a12f' : '#e67e22'}`"> {{ pumpHouseNodeDetail.IsEnd ? '已完成' : '未完成' }}_ </span>
                        相关改造
                      </div>

                      <div class="node-time">
                        <wd-calendar disabled label="完成时间" v-model="pumpHouseNodeDetail.CompletionTime" />
                      </div>

                      <div class="node-remark">
                        <wd-textarea disabled label="备注信息" auto-height label-width="100px" type="textarea" v-model="pumpHouseNodeDetail.Remark" :maxlength="200" show-word-limit placeholder="无备注信息" clearable prop="content" />
                      </div>

                      <div class="node-files" v-if="pumpHouseNodeDetail?.Files">
                        <div class="files-title">
                          <wd-icon name="folder" size="16px" color="#3498DB"></wd-icon>
                          <span>已上传文件</span>
                        </div>
                        <div class="files-list">
                          <template v-for="fileItem in pumpHouseNodeDetail.Files" :key="fileItem.Id">
                            <div class="file-item">
                              <wd-icon name="document" size="14px" color="#3498DB"></wd-icon>
                              <span class="file-name">{{ fileItem.FileName }}</span>
                            </div>
                          </template>
                        </div>
                      </div>
                    </div>
                    <wd-status-tip v-else image="content" tip="暂无节点内容" />
                  </div>
                </wd-tab>
              </block>
            </wd-tabs>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <wd-button type="success" @click="openNavigation" icon="location" custom-class="nav-button">导航</wd-button>
        <wd-button icon="edit-1" @click="openUpdate" custom-class="edit-button">修改</wd-button>
      </div>
      <div style="height: 20px"></div>
    </div>
    <div v-else class="pad-X24">
      <div v-if="detail">
        <wd-icon custom-class="absolute close_icon" @click="height = 0" name="close" size="18px"></wd-icon>

        <!-- 优化的编辑模式标题 -->
        <div class="edit-header">
          <div class="edit-title">{{ detail?.PumpHouseName }}</div>
          <div class="edit-subtitle">
            <wd-icon name="edit-1" size="16px" color="#667eea"></wd-icon>
            <span>编辑模式</span>
          </div>
        </div>

        <!-- 优化的位置信息编辑表单 -->
        <div class="edit-form-section">
          <div class="form-section-header">
            <wd-icon name="location" size="18px" color="#9B59B6"></wd-icon>
            <span>位置信息编辑</span>
            <div class="location-update-btn" @click="open = true">
              <wd-icon name="refresh" size="14px" color="#fff"></wd-icon>
              <span>更新坐标</span>
            </div>
          </div>

          <div class="form-content">
            <!-- 坐标信息展示 -->
            <div class="coordinate-info">
              <div class="coordinate-item">
                <span class="coord-label">经度</span>
                <span class="coord-value">{{ formatValue(detail.X) }}</span>
              </div>
              <div class="coordinate-item">
                <span class="coord-label">纬度</span>
                <span class="coord-value">{{ formatValue(detail.Y) }}</span>
              </div>
            </div>

            <!-- 地址信息 -->
            <div class="address-grid">
              <div class="address-item">
                <div class="address-label">所属街道</div>
                <div class="address-value">{{ formatValue(detail.BelongingStreet) }}</div>
              </div>
              <div class="address-item">
                <div class="address-label">所属片区</div>
                <div class="address-value">{{ formatValue(detail.BelongingArea) }}</div>
              </div>
              <div class="address-item">
                <div class="address-label">所属网格</div>
                <div class="address-value">{{ formatValue(detail.Gridding) }}</div>
              </div>
            </div>

            <!-- 精确位置编辑 -->
            <div class="position-edit">
              <div class="edit-label">
                <wd-icon name="edit-1" size="14px" color="#4A90E2"></wd-icon>
                <span>泵房精确位置</span>
              </div>
              <div class="textarea-wrapper">
                <wd-textarea
                  auto-height
                  type="textarea"
                  v-model="detail.AccuratePosition"
                  :maxlength="200"
                  show-word-limit
                  placeholder="请输入精确位置，如：xxx小区xxx栋地下二层"
                  clearable
                  prop="AccuratePosition"
                  custom-class="custom-textarea"
                />
              </div>
            </div>

            <!-- 图片上传区域 -->
            <div class="image-upload-section">
              <div class="upload-header">
                <wd-icon name="camera" size="16px" color="#50C878"></wd-icon>
                <span>泵房图片</span>
                <div class="upload-tip">最多上传3张</div>
              </div>
              <div class="upload-wrapper">
                <UploadeImg :maxlength="3" :handleImg="({ data }) => data" :url v-model="detail.PumpHouseImg" />
              </div>
            </div>
          </div>
        </div>

        <!-- 优化的操作按钮 -->
        <div class="edit-action-buttons">
          <wd-button type="success" @click="openNavigation" icon="location" custom-class="nav-btn"> 导航 </wd-button>
          <wd-button @click="updatePumphouse" custom-class="save-btn">
            <wd-icon name="check" size="16px" color="#fff"></wd-icon>
            保存修改
          </wd-button>
        </div>
      </div>
    </div>
  </wd-floating-panel>

  <CoordinatePickup v-if="detail && open" v-model="open" :MarkerCenter @change="change" />
  <FeedbackPopup ref="feedbackRef" :id="5" />
  <!-- 搜索列表弹窗 -->
  <wd-popup :modal="false" v-model="searchOpen" closable position="bottom" custom-style="min-height: 1000rpx; z-index:99">
    <div class="pad-24">
      <div class="pad-Y32"></div>
      <div class="flex f-between" v-if="searchType == '泵房'" style="background-color: #32cbdb">
        <div class="f-1 pad-16 text-center color-white">泵房名称</div>
        <div class="f-1 pad-16 text-center color-white">改造状态</div>
        <div class="f-1 pad-16 text-center color-white">系统编号</div>
      </div>
      <div class="flex f-between" v-else style="background-color: #32cbdb">
        <div class="f-1 pad-16 text-center color-white">小区名称</div>
        <div class="f-1 pad-16 text-center color-white">小区编码</div>
      </div>
      <div class="overflow-auto" style="max-height: 800rpx">
        <template v-for="(item, index) in searchList" :key="index">
          <div class="item pad-16 mar-Y12 border-B-eee flex f-between" @click="searchItemClick(item)" v-if="searchType == '泵房'">
            <div class="f-1 text-center" style="color: #333">{{ item.name }}</div>
            <div class="f-1 text-center" style="color: dodgerblue">{{ item.RemouldState }}</div>
            <div class="f-1 text-center">
              <div style="color: #13a12f">{{ item.SYSTEMID }}</div>
            </div>
          </div>
          <div class="item pad-16 mar-Y12 border-B-eee flex f-between" @click="searchItemClick(item)" v-else>
            <div class="f-1 text-center">{{ item.name }}</div>
            <div class="f-1 text-center">{{ item.Zone_Code }}</div>
          </div>
        </template>
        <wd-status-tip v-if="searchList.length == 0" image="search" tip="当前搜索无结果" />
      </div>
    </div>
  </wd-popup>
  <wd-toast />
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { onShow, onLoad, onNavigationBarButtonTap, onBackPress } from '@dcloudio/uni-app'
import { PumpHouseApi } from '/src/services/model/pump.house.js'
import { CommonApi } from '/src/services/model/common.js'
import { mapNavigation } from '/src/utils'
import MapBox from './components/MapBox/index.vue'
import CoordinatePickup from './components/CoordinatePickup/index.vue'
import UploadeImg from '/src/components/UploadeImg/index.vue'
import debounce from 'lodash/debounce'
import { getZoneDataLike } from '/src/services/model/map.js'
import FeedbackPopup from '/src/components/FeedbackPopup/index.vue'

import uniUtil from '/src/utils/uniUtil.js'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const feedbackRef = ref(null)

// 监听按钮点击事件
onNavigationBarButtonTap(() => feedbackRef.value.getFeedbackDetail())
onBackPress(() => {
  const isShow = feedbackRef.value.getFeedbackIsOpen()
  if (height.value !== 0 || searchOpen.value === true || isShow) {
    searchOpen.value = false
    height.value = 0
    feedbackRef.value.close()
    return true
  }
})

onShow(async () => {
  if (!isCollect.value && detail.value) {
    const { data } = await PumpHouseApi.detail(detail.value.PumpRoomNumber)
    detail.value = data
  }
  getPumpHouseList({ all: true })
})
const variation = ref(null)
const userInfo = uniUtil.get('userInfo')

const height = ref(0)
const anchors = ref([])
const seekText = ref('')
const isCollect = ref(false)
const isLegendCollapsed = ref(false) // 默认折叠状态
const open = ref(false)
const MarkerCenter = ref(null)
const batchKeys = { 1: '已纳改:利源代建', 2: '已纳改:查漏补缺', 3: '需纳改', 4: '无需纳改', 5: '已纳改:应改未改' }

// 移除不需要的折叠面板变量
const pumpHouseNode = ref([])
const pumpHouseNodeKeys = computed(() => pumpHouseNode.value.map((item) => item.DictValue).slice(0, detail.value.CurrentNode))
const url = computed(() => `https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${detail.value.PumpRoomNumber}/img`)

onLoad(async ({ PumpRoomNumber }) => {
  const windowHeight = uni.getSystemInfoSync().windowHeight
  anchors.value = [0, Math.round(1 * windowHeight)]
  height.value = anchors.value[0]
  pumpHouseNode.value = await queryDictionaries('pumpHouseNode')
  if (PumpRoomNumber) {
    height.value = anchors.value[1]
    const { data } = await PumpHouseApi.detail(PumpRoomNumber)
    detail.value = data
  }
})

const pumpHouseDots = ref([])
const legend = ref({
  节点: {
    异常: {
      value: 0,
      name: '异常',
      url: 'https://www.szwgft.cn/nodeServer/img/1.png'
    },
    临供: {
      value: 0,
      name: '临供',
      url: 'https://www.szwgft.cn/nodeServer/img/2.png'
    },
    切换: {
      value: 0,
      name: '切换',
      url: 'https://www.szwgft.cn/nodeServer/img/3.png'
    },
    初验: {
      value: 0,
      name: '初验',
      url: 'https://www.szwgft.cn/nodeServer/img/4.png'
    }
  },
  采集: {
    已采集: {
      value: 0,
      name: '已采集',
      url: 'https://www.szwgft.cn/nodeServer/img/4.png'
    },
    未采集: {
      value: 0,
      name: '未采集',
      url: 'https://www.szwgft.cn/nodeServer/img/2.png'
    }
  }
})

// 加载泵房列表
async function getPumpHouseList() {
  const P = {
    节点: {
      异常: {
        value: 0,
        name: '异常',
        url: 'https://www.szwgft.cn/nodeServer/img/1.png'
      },
      临供前: {
        value: 0,
        name: '临供前',
        url: 'https://www.szwgft.cn/nodeServer/img/6.png'
      },
      临供: {
        value: 0,
        name: '临供',
        url: 'https://www.szwgft.cn/nodeServer/img/2.png'
      },
      切换: {
        value: 0,
        name: '切换',
        url: 'https://www.szwgft.cn/nodeServer/img/3.png'
      },
      初验: {
        value: 0,
        name: '初验',
        url: 'https://www.szwgft.cn/nodeServer/img/4.png'
      }
    },
    采集: {
      已采集: {
        value: 0,
        name: '已采集',
        url: 'https://www.szwgft.cn/nodeServer/img/4.png'
      },
      未采集: {
        value: 0,
        name: '未采集',
        url: 'https://www.szwgft.cn/nodeServer/img/2.png'
      }
    }
  }
  toast.loading('加载中..')
  try {
    const { data } = await PumpHouseApi.list()
    pumpHouseDots.value = data.map((item) => {
      const state = item.ProgressStatus != '正常' ? '异常' : item.CurrentNode < 4 ? '临供前' : item.CurrentNode < 12 ? '临供' : item.CurrentNode < 14 ? '切换' : '初验'
      P['节点'][state].value += 1
      const isCollect = item.AccuratePosition ? '已采集' : '未采集'
      P['采集'][isCollect].value += 1
      item.Id = JSON.stringify(item)
      return {
        type: 'Feature',
        geometry: { type: 'Point', coordinates: [item.X, item.Y] },
        properties: { ...item, state, isCollect }
      }
    })

    legend.value = P
    variation.value = { type: 'updatePumpHouseDots', value: pumpHouseDots.value }
    toast.close()
  } catch (error) {
    toast.close()
    toast.error('泵房数据加载失败' + error.message)
    console.log('泵房数据加载失败', error)
  }
}

const detail = ref(null)
const CurrentNode = ref(0)
function handlePumpHouseClick(item) {
  detail.value = JSON.parse(item.Id)
  CurrentNode.value = detail.value.CurrentNode - 1
  searchOpen.value = false
  height.value = anchors.value[1]
  MarkerCenter.value = [detail.value.X, detail.value.Y]
}

const pumpHouseNodeDetail = ref(null)
const pumpHouseNodeFileKeyMap = new Map()
watch(CurrentNode, async (val) => {
  if (!pumpHouseNodeFileKeyMap.has(val + 1)) {
    const { data: FileKey } = await CommonApi.queryDictionaries(`pumpHouseNodeFile_${val + 1}`)
    const data = FileKey.map(({ DictCode, DictValue }) => [DictCode, DictValue])
    pumpHouseNodeFileKeyMap.set(val + 1, Object.fromEntries(data))
  }
  const { data } = await PumpHouseApi.nodeDetail(detail.value.PumpRoomNumber, val + 1)
  if (data?.Files) {
    const nodeFiles = JSON.parse(data.Files)
    const pumpHouseNodeFileKey = pumpHouseNodeFileKeyMap.get(val + 1)
    data.Files = nodeFiles.map((file) => {
      file.FileName = pumpHouseNodeFileKey[file.FileType]
      return file
    })
  }

  pumpHouseNodeDetail.value = data
})
watch(height, (val) => {
  if (val === 0) {
    detail.value = null
  }
})
watch(isCollect, (val) => {
  variation.value = { type: 'cutCollect', value: { isCollect: val } }
  if (val) {
    // legend.value = pumpHouseDots.value.forEach(item)
  }
})

// 获取节点列表

async function queryDictionaries(type) {
  try {
    const { data } = await CommonApi.queryDictionaries(type)
    return data
  } catch (error) {
    message.error(error.message)
  }
}

async function openNavigation() {
  const { data } = await CommonApi.coordinateTransformation({ lat: detail.value.Y, lng: detail.value.X })
  const lat = Number(detail.value.Y) + Number(data.lat.replace(detail.value.Y, ''))
  const lng = Number(detail.value.X) + Number(data.lng.replace(detail.value.X, ''))
  mapNavigation({ lat, lng, name: detail.value.PumpHouseName })
}

function openUpdate() {
  uni.navigateTo({ url: `/src/pages/mine/children/pump-house/pumpHouseUpdate?PumpRoomNumber=${detail.value.PumpRoomNumber}` })
}

// 切换图例折叠状态
function toggleLegend() {
  isLegendCollapsed.value = !isLegendCollapsed.value
}

function previewImage(url) {
  uni.previewImage({
    urls: detail.value.PumpHouseImg.split(','), // 需要预览的图片链接列表
    current: url // 当前显示图片的链接
  })
}

async function handleOneself() {
  toast.loading('正在获取位置')
  const { latitude, longitude } = await uni.getLocation({ altitude: true })
  variation.value = { type: 'moveto', value: [longitude, latitude] }
  toast.close()
}

function change(e) {
  detail.value.X = e.x.toString()
  detail.value.Y = e.y.toString()
  detail.value.Gridding = e.feature.Grid.replace('FD', '福东').replace('FZ', '福中').replace('ML', '梅林').replace('XM', '香蜜')
  detail.value.BelongingStreet = e.feature.Subdistric
  detail.value.BelongingArea = e.feature.Belong_Are
  open.value = false
}

async function updatePumphouse() {
  try {
    await PumpHouseApi.update(detail.value)
    toast.success('泵房信息更新成功')
    getPumpHouseList({ all: true })
    setTimeout(() => (height.value = 0), 1500)
  } catch (error) {
    console.log(error)

    toast.error('泵房信息更新失败')
  }
}

function addInformation() {
  variation.value = { type: 'addMapMarker', value: true }
}

const searchType = ref('泵房')
const searchOpen = ref(false)
const menu = ref([{ content: '泵房' }, { content: '区块' }])

// 搜索
const searchList = ref([])
const debounceHandlesearchChange = debounce(searchChange, 500)
async function searchChange({ value }) {
  if (!value) return
  if (searchType.value === '区块') {
    const res = await getZoneDataLike(value)
    const result = JSON.parse(res.data)
    searchList.value = result.map((i) => ({ name: i.Zone_Name, Zone_Code: i.Zone_Code, Center_Point: i.Center_Point.split(',') }))
  } else {
    const { data } = await PumpHouseApi.seek(seekText.value)
    searchList.value = data.map((item) => ({ ...item, name: item.PumpHouseName }))
  }
  searchOpen.value = true
}

// 搜索内容点击
function searchItemClick(item) {
  if (searchType.value === '区块') {
    variation.value = { type: 'reach', value: item }
  } else {
    const { X, Y } = item
    variation.value = { type: 'moveto', value: [X, Y] }
  }
  searchOpen.value = false
}

function changeSearchType({ item }) {
  searchType.value = item.content
  searchOpen.value = false
  seekText.value = ''
}

// 新增的辅助函数
function getStatusClass(status) {
  const statusMap = {
    已完成: 'status-completed',
    进行中: 'status-progress',
    未开始: 'status-pending',
    异常: 'status-error'
  }
  return statusMap[status] || 'status-default'
}

function getProgressClass(progress) {
  const progressMap = {
    正常: 'progress-normal',
    延期: 'progress-delayed',
    异常: 'progress-error'
  }
  return progressMap[progress] || 'progress-default'
}

// 处理空值显示的辅助函数
function formatValue(value, suffix = '') {
  if (value === null || value === undefined || value === '') {
    return '--'
  }
  // 对于数字0，如果有后缀则显示，否则显示0
  if (value === 0) {
    return suffix ? `${value}${suffix}` : value
  }
  return suffix ? `${value}${suffix}` : value
}

// 检查值是否为空
function isEmpty(value) {
  return value === null || value === undefined || value === '' || value === '--'
}

// 处理坐标显示
function formatCoordinate(x, y) {
  if (!x || !y) {
    return '--'
  }
  return `${x}, ${y}`
}
</script>

<style lang="less" scoped>
.close_icon {
  top: 28rpx;
  right: 18px;
  z-index: 99;
}

.floating-panel_c {
  background-color: rgba(255, 255, 255, 0.7);
  :deep(.wd-floating-panel__header) {
    height: 14px;
  }
  :deep(.wd-floating-panel__content) {
    background-color: transparent;
  }
}

// 优化的泵房详情样式
.pump-house-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 20rpx 0 30rpx 0;
  padding: 30rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  color: white;
}

.pump-house-title {
  font-size: 32rpx;
  font-weight: 700;
  text-align: center;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.status-info {
  display: flex;
  justify-content: center;
  gap: 12rpx;
  align-items: center;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.batch-info {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}

// 状态样式
.status-completed {
  background: linear-gradient(135deg, #4caf50, #45a049) !important;
}

.status-progress {
  background: linear-gradient(135deg, #ff9800, #f57c00) !important;
}

.status-pending {
  background: linear-gradient(135deg, #9e9e9e, #757575) !important;
}

.status-error {
  background: linear-gradient(135deg, #f44336, #d32f2f) !important;
}

// 信息区块样式
.info-section {
  margin-bottom: 24rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #e9ecef;
}

.info-content {
  padding: 24rpx;
}

// 信息行布局
.info-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.info-row.full {
  flex-direction: column;
}

.info-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #4a90e2;
  transition: all 0.3s ease;
}

.info-item.important {
  border-left-color: #ff6b6b;
  background: linear-gradient(135deg, #fff5f5, #fef2f2);
}

.label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.value {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
  word-break: break-all;
}

.value.highlight {
  color: #4a90e2;
  font-weight: 700;
}

.value.warning {
  color: #ff6b6b;
}

.value.success {
  color: #50c878;
}

.value.remark {
  font-size: 24rpx;
  line-height: 1.6;
  color: #666;
}

.phone-number {
  color: #4a90e2;
  font-weight: 600;
}

// 空值样式
.empty-value {
  color: #999 !important;
  font-style: italic;
  opacity: 0.8;
}

// 坐标显示样式
.coordinate-display {
  background: linear-gradient(135deg, #f3e5f5, #fce4ec);
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  margin-bottom: 20rpx;
}

.coordinate-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.coordinate-value {
  font-size: 28rpx;
  font-weight: 700;
  color: #9b59b6;
  font-family: 'Courier New', monospace;
}

// 图片展示样式
.image-section {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #e9ecef;
}

.image-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 16rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.image-item:active {
  transform: scale(0.95);
}

.pump-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 节点信息样式
.node-content {
  min-height: 400rpx;
  padding: 24rpx;
}

.node-detail {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.node-status {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.status-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.node-time,
.node-remark {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.node-files {
  background: linear-gradient(135deg, #f0f8ff, #e6f3ff);
  border-radius: 12rpx;
  padding: 20rpx;
}

.files-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e1e8ed;
}

.file-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

// 节点信息样式优化
.node-tabs {
  :deep(.wd-tabs__nav) {
    background: #f8f9fa;
    border-radius: 12rpx 12rpx 0 0;
  }

  :deep(.wd-tab) {
    font-size: 24rpx;
    font-weight: 600;
  }
}

// 操作按钮样式
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin: 40rpx 50rpx 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #e9ecef;
}

.action-buttons .wd-button {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

// 编辑模式样式
.edit-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 20rpx 0 30rpx 0;
  padding: 30rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  color: white;
  text-align: center;
}

.edit-title {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.edit-subtitle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 24rpx;
  opacity: 0.9;
}

.edit-form-section {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 30rpx;
}

.form-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1rpx solid #e9ecef;
}

.form-section-header > span {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-left: 12rpx;
}

.location-update-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.location-update-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #8e44ad, #7d3c98);
}

.form-content {
  padding: 24rpx;
}

.coordinate-info {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f3e5f5, #fce4ec);
  border-radius: 12rpx;
}

.coordinate-item {
  flex: 1;
  text-align: center;
}

.coord-label {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.coord-value {
  display: block;
  font-size: 26rpx;
  font-weight: 700;
  color: #9b59b6;
  font-family: 'Courier New', monospace;
}

.address-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.address-item {
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 3rpx solid #4a90e2;
}

.address-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.address-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
}

.position-edit {
  margin-bottom: 24rpx;
}

.edit-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.textarea-wrapper {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 4rpx;
}

.custom-textarea {
  :deep(.wd-textarea__inner) {
    background: white;
    border-radius: 8rpx;
    transition: border-color 0.3s ease;
  }

  :deep(.wd-textarea__inner:focus) {
    border-color: #4a90e2;
    box-shadow: 0 0 0 2rpx rgba(74, 144, 226, 0.2);
  }
}

.image-upload-section {
  padding-top: 24rpx;
  border-top: 1rpx solid #e9ecef;
}

.upload-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.upload-header > span {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-left: 8rpx;
}

.upload-tip {
  font-size: 22rpx;
  color: #999;
  background: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.upload-wrapper {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
  border: 2rpx dashed #d0d0d0;
  transition: all 0.3s ease;
}

.upload-wrapper:hover {
  border-color: #50c878;
  background: #f0fff4;
}

.edit-action-buttons {
  display: flex;
  gap: 20rpx;
  margin: 40rpx 50rpx 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #e9ecef;
}

.nav-btn,
.save-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.save-btn {
  background: linear-gradient(135deg, #ff6b6b, #e55353);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.save-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

:deep(.wd-tabs__map-nav-btn) {
  width: auto;
  height: auto;
  padding: 8rpx 18rpx;
  margin-bottom: 10rpx;
  line-height: normal;
}

/* 优化的图例样式 - 参照info-section设计 */
.map-legend {
  bottom: 44rpx;
  left: 24rpx;
  border-radius: 12rpx;
  background: #fff;
  min-width: 200rpx;
  max-width: 260rpx;
  transition: all 0.3s ease;
}

.legend-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: background 0.2s ease;
}

.legend-header:active {
  background: #f8f9fa;
}

.legend-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.title-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.legend-count {
  font-size: 20rpx;
  color: #999;
  margin-left: 6rpx;
}

.legend-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28rpx;
  height: 28rpx;
  transition: transform 0.2s ease;
}

.legend-content {
  max-height: 400rpx;
  overflow-y: auto;
  overflow-x: hidden;
  transition: all 0.3s ease;
}

.legend-content.collapsed {
  max-height: 0;
  overflow: hidden;
}

.collect-section {
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.collect-label {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #333;
}

.legend-items {
  padding: 12rpx 20rpx 16rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 8rpx 0;
  gap: 12rpx;
}

.legend-item:last-child {
  padding-bottom: 0;
}

.legend-icon {
  width: 24rpx;
  height: 24rpx;
  flex-shrink: 0;
}

.legend-name {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.legend-value {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

/* 滚动条样式 */
.legend-content::-webkit-scrollbar {
  width: 4rpx;
}

.legend-content::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 2rpx;
}

.legend-content::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

/* 移动端优化 */
@media (max-width: 750rpx) {
  .map-legend {
    max-width: 220rpx;
  }

  .legend-content {
    max-height: 350rpx;
  }

  .title-text {
    font-size: 24rpx;
  }

  .legend-name {
    font-size: 22rpx;
  }

  .legend-value {
    font-size: 20rpx;
  }
}

.updateIcon {
  top: 16rpx;
  right: 26rpx;
  padding: 4rpx 18rpx;
  background: #15a131;
  border-radius: 12rpx;
}

.search-type {
  position: relative;
  height: 30px;
  line-height: 30px;
  padding: 0 8px 0 16px;
}

.search-type::after {
  position: absolute;
  content: '';
  width: 1px;
  right: 0;
  top: 5px;
  bottom: 5px;
  background: rgba(0, 0, 0, 0.25);
}

.search-type {
  :deep(.icon-arrow) {
    display: inline-block;
    font-size: 20px;
    vertical-align: middle;
  }
}

.item {
  background-color: #eee;
}
.item:nth-child(2n) {
  background-color: antiquewhite;
}
</style>
