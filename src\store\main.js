import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getRecordList } from '/src/services/model/map.js'
import uniUtil from '/src/utils/uniUtil.js'

export const useMainState = defineStore('main', () => {
  const records = ref({
    list: [],
    resolved: [],
    unsolved: []
  })

  async function getRecord() {
    const resolved = []
    const unsolved = []
    const { station } = uniUtil.get('userInfo')
    const res = await getRecordList(station)
    records.value.list = res.data.operationFeedbacks
    records.value.list.forEach((item) => (item.path4 ? resolved.push(item) : unsolved.push(item)))
    records.value.resolved = resolved
    records.value.unsolved = unsolved
  }

  return {
    records,
    getRecord
  }
})
