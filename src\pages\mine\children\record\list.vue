<template>
  <div class="record-list all flex f-column">
    <wd-tabs v-model="tabWithBadge" @change="handleChange" custom-class="box-shadow">
      <wd-tab v-for="(item, index) in tabsWithBadge" :key="index" :title="item.title" :badge-props="item.badgeProps"> </wd-tab>
    </wd-tabs>
    <div class="f-1 overflow-auto pad-16">
      <template v-for="item in MaintainList" :key="item.ID">
        <div @click="ItemClick(item)" class="record-card mar-B16 pad-24 back-white box-shadow border-R10" :class="getStatusClass(item)">
          <!-- 卡片头部 -->
          <div class="card-header f-between">
            <div class="facility-info f-y-center">
              <div class="facility-icon f-xy-center mar-R12">
                <wd-icon :name="getFacilityIcon(item.Facility_Type)" size="16px" color="#fff"></wd-icon>
              </div>
              <div>
                <div class="facility-title fon-S28 fon-W600 color-primary">{{ item.Facility_Type }}</div>
                <div class="task-type fon-S22 color-999 mar-T4">{{ item.Task_Type }}</div>
              </div>
            </div>
            <div class="status-info text-right">
              <div class="status-tag" :class="getStatusTagClass(item)">{{ getStatusText(item) }}</div>
              <div class="date-info fon-S22 color-666 mar-T8">{{ item.INPUTDATE.slice(0, 10) }}</div>
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content mar-T16">
            <div class="remark-section" v-if="item.Remark1">
              <div class="remark-label fon-S20 color-999 mar-B4">备注信息</div>
              <div class="remark-text fon-S24 color-666 text-nowrap-2">{{ item.Remark1 }}</div>
            </div>
            <div class="staff-info f-between mar-T12">
              <div class="staff-item">
                <div class="staff-label fon-S20 color-999">上报人员</div>
                <div class="staff-name fon-S24 color-333 fon-W500">{{ item.INPUTSTAFF }}</div>
              </div>
              <div class="arrow-icon">
                <wd-icon name="arrow-right" color="#ccc" size="18px"></wd-icon>
              </div>
            </div>
          </div>
        </div>
      </template>

      <div v-if="MaintainList.length === 0" class="all f-xy-center">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </div>
    </div>
    <wd-pagination v-model="currentPagesNum" :total="total" :pageSize="pageSize" @change="handlePaginationChange" show-icon />
  </div>
  <wd-toast />
</template>

<script setup>
import { ref } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'
import { MaintainApi } from '/src/services/model/maintain.js'

const toast = useToast()
const currentPagesNum = ref(1) // 当前页数
const pageSize = ref(10) // 每页条数
const total = ref(0) // 总条数

const tabWithBadge = ref(0)
const tabsWithBadge = ref([
  { title: '已处理', badgeProps: { modelValue: 0, right: '-12px' } },
  { title: '待处理', badgeProps: { modelValue: 0, max: 99, right: '-12px' } }
])
onLoad(({ type = 'true' }) => {
  tabWithBadge.value = type == 'true' ? 0 : 1
  // getMaintainList(type, 1)
})

onShow(async () => {
  try {
    const { data } = await MaintainApi.statistics()
    tabsWithBadge.value[0].badgeProps.modelValue = data.countFinish
    tabsWithBadge.value[1].badgeProps.modelValue = data.countUndone
    handlePaginationChange({ value: currentPagesNum.value })
  } catch (error) {
    toast.error('获取数据失败')
  }
})

function handleChange({ index }) {
  const type = index == 0 ? 'true' : 'false'
  currentPagesNum.value = 1
  getMaintainList(type, 1)
}

function handlePaginationChange({ value }) {
  getMaintainList(tabWithBadge.value == 0 ? 'true' : 'false', value)
}

const MaintainList = ref([])
async function getMaintainList(finish, page) {
  toast.loading('加载中')
  try {
    const { data } = await MaintainApi.list(finish, page, pageSize.value)
    const { total: Total, list } = data
    total.value = Total
    MaintainList.value = list
    toast.close()
  } catch (error) {
    toast.close()
    toast.error('数据获取失败')
  }
}

function ItemClick(item) {
  uni.navigateTo({ url: `/src/pages/mine/children/record/detail?id=${item.Id}` })
}

// 获取设施类型图标
function getFacilityIcon(facilityType) {
  const iconMap = {
    总阀: 'setting',
    总表: 'chart',
    管道: 'link',
    泵房: 'home',
    水表: 'chart-pie',
    阀门: 'setting',
    管网: 'link',
    设备: 'tool'
  }
  return iconMap[facilityType] || 'tool'
}

// 获取状态样式类
function getStatusClass(item) {
  // 根据tabWithBadge判断是已处理还是待处理
  const isFinished = tabWithBadge.value === 0
  return isFinished ? 'status-finished' : 'status-pending'
}

// 获取状态标签样式类
function getStatusTagClass(item) {
  const isFinished = tabWithBadge.value === 0
  return isFinished ? 'tag-finished' : 'tag-pending'
}

// 获取状态文本
function getStatusText(item) {
  const isFinished = tabWithBadge.value === 0
  return isFinished ? '已处理' : '待处理'
}
</script>

<style lang="less" scoped>
:deep(.wd-badge__content) {
  height: auto;
}

// 记录卡片样式
.record-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
  }

  // 状态左边框
  &.status-finished {
    border-left: 6rpx solid #52c41a;
  }

  &.status-pending {
    border-left: 6rpx solid #faad14;
  }
}

// 卡片头部
.card-header {
  align-items: flex-start;
}

// 设施信息
.facility-info {
  flex: 1;
}

.facility-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%);
  box-shadow: 0 2rpx 8rpx rgba(77, 99, 224, 0.3);
}

.facility-title {
  color: #4d63e0;
  line-height: 1.2;
}

.task-type {
  line-height: 1.2;
}

// 状态信息
.status-info {
  align-items: flex-end;
}

.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1;
  display: inline-block;

  &.tag-finished {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    color: #52c41a;
    border: 1rpx solid #b7eb8f;
  }

  &.tag-pending {
    background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
    color: #faad14;
    border: 1rpx solid #ffe58f;
  }
}

.date-info {
  line-height: 1.2;
}

// 卡片内容
.card-content {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

// 备注区域
.remark-section {
  background: rgba(77, 99, 224, 0.04);
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  border-left: 3rpx solid #4d63e0;
}

.remark-label {
  line-height: 1.2;
}

.remark-text {
  line-height: 1.4;
}

// 员工信息
.staff-info {
  align-items: center;
}

.staff-item {
  flex: 1;
}

.staff-label {
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.staff-name {
  line-height: 1.2;
}

.arrow-icon {
  opacity: 0.6;
  transition: all 0.3s ease;
}

.record-card:active .arrow-icon {
  opacity: 1;
  transform: translateX(4rpx);
}

// 响应式优化
@media (max-width: 750rpx) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }

  .status-info {
    align-items: flex-start;
  }
}
</style>
