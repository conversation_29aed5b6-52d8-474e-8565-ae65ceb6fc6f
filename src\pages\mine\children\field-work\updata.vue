<template>
  <view class="update-page">
    <wd-form ref="form" :model="model" :rules="rules">
      <!-- 基础信息卡片 -->
      <view class="form-section">
        <view class="section-header">
          <view class="header-icon">
            <wd-icon name="layers" color="#1890ff" size="20" />
          </view>
          <view class="header-content">
            <text class="section-title">基础信息</text>
            <text class="section-subtitle">修改总阀总表的基本参数</text>
          </view>
          <view class="header-badge">编辑</view>
        </view>

        <view class="form-content">
          <wd-cell-group custom-class="form-group" border>
            <!-- 点子类选择 -->
            <view class="field-wrapper">
              <wd-cell required title="点子类" title-width="120rpx" prop="point_Type" center>
                <wd-radio-group disabled v-if="selectOption?.fieldWorkFacility" v-model="model.point_Type" shape="button" custom-class="radio-group-modern">
                  <template v-for="item in selectOption.fieldWorkFacility" :key="item.Id">
                    <wd-radio :value="item.DictValue" custom-class="radio-modern">{{ item.DictValue }}</wd-radio>
                  </template>
                </wd-radio-group>
              </wd-cell>
            </view>

            <!-- 生消类型 -->
            <view v-if="model.point_Type !== '立管点'" class="field-wrapper">
              <wd-cell required title="生消类型" title-width="140rpx" prop="type" center>
                <wd-radio-group v-if="selectOption?.fieldWorkFireControl" v-model="model.type" shape="button" custom-class="radio-group-modern">
                  <template v-for="item in selectOption.fieldWorkFireControl" :key="item.Id">
                    <wd-radio :value="item.DictValue" custom-class="radio-modern">{{ item.DictValue }}</wd-radio>
                  </template>
                </wd-radio-group>
              </wd-cell>
            </view>

            <!-- 埋深输入 -->
            <view v-if="model.point_Type !== '立管点'" class="field-wrapper">
              <wd-cell required title="埋深" title-width="120rpx" prop="burial_Depth" center>
                <view class="input-with-unit">
                  <wd-input type="number" custom-class="modern-input" clearable v-model="model.burial_Depth" placeholder="请输入埋深" />
                  <view class="unit-label">m</view>
                </view>
              </wd-cell>
            </view>

            <!-- 管顶离地高度 -->
            <view v-if="model.point_Type !== '立管点'" class="field-wrapper">
              <wd-cell required title="管顶离地高度" title-width="120rpx" prop="to_Distance" center>
                <view class="input-with-unit">
                  <wd-input type="number" custom-class="modern-input" clearable v-model="model.to_Distance" placeholder="请输入管顶离地高度" />
                  <view class="unit-label">m</view>
                </view>
              </wd-cell>
            </view>

            <!-- 管径 -->
            <view class="field-wrapper">
              <wd-cell required title="管径" title-width="120rpx" prop="pipe_Diameter" center>
                <view class="input-with-unit">
                  <wd-input type="number" custom-class="modern-input pad-X12" clearable v-model="model.pipe_Diameter" placeholder="请输入管径" />
                  <view class="unit-label">mm</view>
                </view>
              </wd-cell>
            </view>

            <!-- 管材选择 -->
            <view class="field-wrapper">
              <wd-picker
                v-if="model.point_Type !== '立管点'"
                required
                label="管材"
                placeholder="选择管材"
                label-width="120rpx"
                prop="pipe_Material"
                v-model="model.pipe_Material"
                :columns="selectOption.fieldWorkPipelineMaterial"
                custom-class="modern-picker"
              />
              <wd-picker v-else required label="管材" placeholder="选择管材" label-width="120rpx" prop="pipe_Material" v-model="model.pipe_Material" :columns="selectOption.fieldWorkRiserPointMaterial" custom-class="modern-picker" />
            </view>

            <!-- 进水路数 -->
            <view v-if="model.point_Type !== '立管点'" class="field-wrapper">
              <wd-input required label="进水路数" label-width="160rpx" show-word-limit prop="inlet_Waterways" suffix-icon="warn-bold" clearable v-model="model.inlet_Waterways" placeholder="请输入进水路数" custom-class="modern-input" />
            </view>

            <!-- 阀体材料 -->
            <view v-if="model.point_Type !== '立管点'" class="field-wrapper">
              <wd-picker required label="阀体材料" placeholder="选择阀体材料" label-width="140rpx" prop="valve_Material" v-model="model.valve_Material" :columns="selectOption.fieldWorkValveBodyMateria" custom-class="modern-picker" />
            </view>

            <!-- 水表类型 -->
            <view v-if="model.point_Type !== '立管点'" class="field-wrapper">
              <wd-cell required title="水表类型" title-width="140rpx" prop="water_Meter_Type" center>
                <wd-radio-group v-if="selectOption?.fieldWorkWaterMeterType" v-model="model.water_Meter_Type" shape="button" custom-class="radio-group-modern">
                  <template v-for="item in selectOption.fieldWorkWaterMeterType" :key="item.Id">
                    <wd-radio :value="item.DictValue" custom-class="radio-modern">{{ item.DictValue }}</wd-radio>
                  </template>
                </wd-radio-group>
              </wd-cell>
            </view>

            <!-- 水表编码 -->
            <view v-if="model.point_Type !== '立管点'" class="field-wrapper">
              <wd-cell required title="水表编码" title-width="140rpx" prop="water_Meter_Coding" center>
                <view class="input-with-scan">
                  <wd-input custom-class="modern-input pad-X12 scan-input" custom-style="width:380rpx" clearable v-model="model.water_Meter_Coding" placeholder="请输入水表编码" />
                  <UploadeImage url="https://www.szwgft.cn:5000/api/Ocr/OCR" @success="handleUploadSuccess">
                    <view class="scan-button"> <wd-icon name="scan" color="#1890ff" size="18" /> </view>
                  </UploadeImage>
                </view>
              </wd-cell>
            </view>
          </wd-cell-group>
        </view>
      </view>

      <!-- 位置信息卡片 -->
      <view class="form-section">
        <view class="section-header">
          <view class="header-icon">
            <wd-icon name="location" color="#52c41a" size="20" />
          </view>
          <view class="header-content">
            <text class="section-title">位置信息</text>
            <text class="section-subtitle">修改准确的地理位置信息</text>
          </view>
          <view class="header-badge location-badge">定位</view>
        </view>

        <view class="form-content">
          <wd-cell-group custom-class="form-group" border>
            <!-- 获取位置按钮 -->
            <view class="field-wrapper location-field">
              <wd-cell title="获取位置信息" required title-width="200rpx" center>
                <view class="location-actions">
                  <view @click="open = true" class="location-btn">
                    <wd-icon name="location" color="#fff" size="14" />
                    <text class="btn-text">获取位置</text>
                  </view>
                  <view @click="getAddress" class="address-btn">
                    <wd-icon name="refresh" color="#1890ff" size="14" />
                    <text class="address-text">获取地址</text>
                  </view>
                </view>
              </wd-cell>
            </view>

            <!-- 小区名称 -->
            <view class="field-wrapper">
              <wd-input label="小区名称" required label-width="160rpx" prop="zone_Name" suffix-icon="warn-bold" clearable v-model="model.zone_Name" placeholder="请获取位置信息" custom-class="modern-input" />
            </view>

            <!-- 坐标信息 -->
            <view class="coordinate-group">
              <view class="coordinate-item">
                <wd-input label="经度(X)" label-width="120rpx" disabled prop="x" required v-model="model.x" custom-class="modern-input coordinate-input" />
              </view>
              <view class="coordinate-item">
                <wd-input label="纬度(Y)" label-width="120rpx" disabled prop="y" required v-model="model.y" custom-class="modern-input coordinate-input" />
              </view>
            </view>

            <!-- 详细地址 -->
            <view class="field-wrapper">
              <wd-textarea
                required
                label="地址"
                auto-height
                label-width="120rpx"
                type="textarea"
                v-model="model.address"
                :maxlength="100"
                show-word-limit
                placeholder="请输入或获取详细地址信息"
                prop="address"
                custom-class="modern-textarea"
              />
            </view>
          </wd-cell-group>
        </view>
      </view>

      <!-- 现场信息卡片 -->
      <view class="form-section">
        <view class="section-header">
          <view class="header-icon">
            <wd-icon name="camera" color="#fa8c16" size="20" />
          </view>
          <view class="header-content">
            <text class="section-title">现场信息</text>
            <text class="section-subtitle">修改现场情况和上传照片</text>
          </view>
          <view class="header-badge photo-badge">拍照</view>
        </view>

        <view class="form-content">
          <wd-cell-group custom-class="form-group" border>
            <!-- 备注信息 -->
            <view class="field-wrapper">
              <wd-textarea
                required
                label="备注"
                prop="notes"
                auto-height
                label-width="120rpx"
                type="textarea"
                v-model="model.notes"
                :maxlength="200"
                show-word-limit
                placeholder="请详细描述现场情况、设备状态等信息"
                clearable
                custom-class="modern-textarea"
              />
            </view>

            <!-- 现场照片 -->
            <view class="field-wrapper photo-field">
              <wd-cell prop="site_Photo" title="现场照片" title-width="120rpx" center>
                <view class="photo-upload-area">
                  <UploadeImg :imageBaseUrl :maxlength="1" :handleImg="({ data }) => data" :url="`${ServerBaseUrl}/api/UpWrite/upload?Zone_Code=${'ValveVerification/' + model.zone_Code}`" v-model="model.site_Photo" class="modern-upload" />
                  <view class="upload-tip">
                    <wd-icon name="camera" color="#8c8c8c" size="16" />
                    <text class="tip-text">请拍摄清晰的现场照片</text>
                  </view>
                </view>
              </wd-cell>
            </view>
          </wd-cell-group>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <wd-button type="primary" size="large" @click="handleSubmit" block custom-class="submit-button">
          <wd-icon name="edit" size="18" />
          <text style="margin-left: 12rpx">提交修改</text>
        </wd-button>
      </view>
    </wd-form>
  </view>
  <CoordinatePickup v-if="open" v-model="open" :MarkerCenter @change="change" />

  <canvas class="canvas" canvas-id="mycanvas" id="mycanvas"> </canvas>

  <!-- OCR识别结果弹窗 -->
  <wd-popup :close-on-click-modal="false" closable v-model="textPopupOpen" lock-scroll custom-style="border-radius:24rpx;width:90%;max-height:70vh" @close="handleClose">
    <view class="ocr-popup">
      <view class="popup-header">
        <view class="popup-title">
          <wd-icon name="scan" color="#1890ff" size="20" />
          <text class="title-text">识别结果</text>
        </view>
        <text class="popup-subtitle">请选择正确的水表编码</text>
      </view>

      <view class="popup-content">
        <view class="text-grid">
          <template v-for="(item, index) in textValue" :key="index">
            <view @click="handleTextClick(index)" :class="{ 'text-item-selected': testSelectValue.includes(index) }" class="text-item">
              {{ item }}
            </view>
          </template>
        </view>
      </view>

      <view class="popup-footer">
        <wd-button type="success" @click="handleTextConfirm" block custom-class="confirm-button">
          <wd-icon name="check" size="16" />
          <text style="margin-left: 8rpx">确认选择</text>
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import CoordinatePickup from './components/CoordinatePickup/index.vue'

import UploadeImage from './components/UploadeImage/index.vue'

import UploadeImg from '/src/components/UploadeImg/index.vue'
import { imageBaseUrl, ServerBaseUrl } from '/src/config/index.json'

import { alterValveVerificationApi, getValveVerificationDetailApi } from '/src/services/model/submit.js'
import { CommonApi } from '/src/services/model/common.js'

import uniUtil from '/src/utils/uniUtil'
import { getCurrentDateTime } from '/src/utils'

let id = null
const MarkerCenter = ref(null)
const textPopupOpen = ref(false)
const selectOption = ref({})
onLoad(async (e) => {
  try {
    id = e.id
    const { data } = await getValveVerificationDetailApi(id)
    model.value = data.valveVerification[0]
    MarkerCenter.value = model.value.coordinates.split(',')

    const { data: fieldWorkFacility } = await CommonApi.queryDictionaries('fieldWorkFacility') //外业点子类
    selectOption.value.fieldWorkFacility = fieldWorkFacility
    const { data: fieldWorkFireControl } = await CommonApi.queryDictionaries('fieldWorkFireControl') //外业生消类型
    selectOption.value.fieldWorkFireControl = fieldWorkFireControl
    const { data: fieldWorkPipelineMaterial } = await CommonApi.queryDictionaries('fieldWorkPipelineMaterial') //外业管材材料类型
    selectOption.value.fieldWorkPipelineMaterial = fieldWorkPipelineMaterial.map((item) => ({ label: item.DictValue, value: item.DictValue }))
    const { data: fieldWorkValveBodyMateria } = await CommonApi.queryDictionaries('fieldWorkValveBodyMateria') //外业阀体材料类型
    selectOption.value.fieldWorkValveBodyMateria = fieldWorkValveBodyMateria.map((item) => ({ label: item.DictValue, value: item.DictValue }))
    const { data: fieldWorkWaterMeterType } = await CommonApi.queryDictionaries('fieldWorkWaterMeterType') //外业阀体材料类型
    selectOption.value.fieldWorkWaterMeterType = fieldWorkWaterMeterType
    const { data: fieldWorkRiserPointMaterial } = await CommonApi.queryDictionaries('fieldWorkRiserPointMaterial') //外业立管点材料类型
    selectOption.value.fieldWorkRiserPointMaterial = fieldWorkRiserPointMaterial.map((item) => ({ label: item.DictValue, value: item.DictValue }))
  } catch (error) {
    uniUtil.showToast(error)
  }
})

const open = ref(false)
const model = ref({
  zone_Name: null, //小名称区
  point_Type: null, //点子类
  unique_Id: null, //唯一 ID
  burial_Depth: null, //埋深
  to_Distance: null, //管顶离地高度
  valve_Material: null, // 阀体材料
  pipe_Material: null, //管材
  pipe_Diameter: null, //管径
  inlet_Waterways: null, //进水路数
  type: null, //生消类型
  address: null, //地址
  coordinates: null, //坐标
  notes: null, //备注
  site_Photo: '', //现场照片
  uploader: null, //上传人
  created_Time: null, //创建时间
  updated_Time: null, //更新时间
  updater: null, //更新人
  zone_Code: null, //小区code
  x: null, //x field-work
  y: null //y
})

function change(e) {
  model.value.x = e.x + ''
  model.value.y = e.y + ''
  model.value.zone_Name = e.zone.Zone_Name
  model.value.zone_Code = e.code
  model.value.coordinates = e.x + ',' + e.y

  getAddress()
}

const form = ref(null)
const rules = {
  zone_Name: [{ required: true, message: '请获取小名称区', validator: (value) => (value ? Promise.resolve() : Promise.reject('请获取小名称区')) }],
  point_Type: [{ required: true, message: '请选择点子类', validator: (value) => (value ? Promise.resolve() : Promise.reject('请选择点子类')) }],
  // burial_Depth: [{ required: true, message: '请填写埋深', validator: (value) => (value ? Promise.resolve() : Promise.reject('请填写埋深')) }],
  // to_Distance: [{ required: true, message: '请填写管顶离地高度', validator: (value) => (value ? Promise.resolve() : Promise.reject('请填写管顶离地高度')) }],
  // valve_Material: [{ required: true, message: '请选择阀体材料', validator: (value) => (value ? Promise.resolve() : Promise.reject('请选择阀体材料')) }],
  // pipe_Material: [{ required: true, message: '管材阀体材料', validator: (value) => (value ? Promise.resolve() : Promise.reject('管材阀体材料')) }],
  // pipe_Diameter: [{ required: true, message: '请填写进水路数', validator: (value) => (value ? Promise.resolve() : Promise.reject('请填写进水路数')) }],
  // type: [{ required: true, message: '请选择生消类型', validator: (value) => (value ? Promise.resolve() : Promise.reject('请选择生消类型')) }],
  // address: [{ required: true, message: '请填写地址', validator: (value) => (value ? Promise.resolve() : Promise.reject('请填写地址')) }],
  // notes: [{ required: true, message: '请填写备注', validator: (value) => (value ? Promise.resolve() : Promise.reject('请填写备注')) }],
  // site_Photo: [{ required: true, message: '请上传现场照片', validator: (value) => (value ? Promise.resolve() : Promise.reject('请上传现场照片')) }],
  x: [{ required: true, message: '必须获取位置信息', validator: (value) => (value ? Promise.resolve() : Promise.reject('必须获取位置信息')) }],
  y: [{ required: true, message: '必须获取位置信息', validator: (value) => (value ? Promise.resolve() : Promise.reject('必须获取位置信息')) }]
}
function handleSubmit() {
  const userInfo = uniUtil.get('userInfo')
  const time = getCurrentDateTime()
  model.value.updater = userInfo.name
  model.value.updated_Time = time
  form.value
    .validate()
    .then(async ({ valid, errors }) => {
      if (valid) {
        try {
          const res = await alterValveVerificationApi(model.value, id)
          if (res.code === 200) {
            uniUtil.showToast('修改成功')
            setTimeout(() => uniUtil.navigateBack(), 2000)
          }
        } catch (error) {
          uniUtil.showToast('修改失败-' + error)
        }
      } else {
        uniUtil.showToast(errors[0].message)
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}

const textValue = ref('')
const testSelectValue = ref([])
function handleUploadSuccess(res) {
  textPopupOpen.value = true
  const text = res.text
    .split('\n')
    .filter((item) => item != '')
    .map((item) => item.trim())
  textValue.value = text.flat()
}

function handleTextClick(index) {
  if (testSelectValue.value.includes(index)) {
    testSelectValue.value = testSelectValue.value.filter((item) => item !== index)
  } else {
    testSelectValue.value.push(index)
  }
}

function handleTextConfirm() {
  model.value.water_Meter_Coding = testSelectValue.value.map((item) => textValue.value[item]).join('')
  testSelectValue.value = []
  textPopupOpen.value = false
}

function handleClose() {
  testSelectValue.value = []
  textPopupOpen.value = false
}

async function getAddress() {
  const [lngx, laty] = model.value.coordinates.split(',')
  const { data } = await CommonApi.coordinateTransformation({ lat: laty, lng: lngx })
  const lat = Number(laty) + Number(data.lat.replace(laty, ''))
  const lng = Number(lngx) + Number(data.lng.replace(lngx, ''))
  const c = lng + ',' + lat

  const res = await uni.request({
    url: 'https://restapi.amap.com/v3/geocode/regeo?key=f05e0f12fdcb52e788bf969726fc66cb&extensions=all&location=' + c,
    method: 'GET'
  })
  const resData = res.data.regeocode
  model.value.address = resData.formatted_address
}
</script>

<style lang="less" scoped>
/* 页面整体样式 */
.update-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8faff 0%, #f0f2f5 100%);
  padding: 24rpx;
  position: relative;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 200rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: -1;
    opacity: 0.1;
  }
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 24rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 区域头部样式 */
.section-header {
  display: flex;
  align-items: center;
  padding: 28rpx 32rpx;
  background: linear-gradient(135deg, #f8faff 0%, #f0f8ff 100%);
  border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
  gap: 20rpx;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 32rpx;
    right: 32rpx;
    height: 2rpx;
    background: linear-gradient(90deg, transparent 0%, #1890ff 50%, transparent 100%);
    opacity: 0.3;
  }
}

.header-icon {
  width: 72rpx;
  height: 72rpx;
  background: linear-gradient(135deg, #fff 0%, #f8faff 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.15);
  border: 2rpx solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

.header-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.section-title {
  display: block;
  font-size: 34rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
  line-height: 1.2;
}

.section-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666;
  font-weight: 400;
  opacity: 0.8;
}

.header-badge {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: #fff;
  font-size: 22rpx;
  font-weight: 600;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &.location-badge {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  }

  &.photo-badge {
    background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
    box-shadow: 0 4rpx 12rpx rgba(250, 140, 22, 0.3);
  }
}

/* 表单内容样式 */
.form-content {
  padding: 0;
  background: #fff;
}

.field-wrapper {
  position: relative;
  transition: all 0.3s ease;

  &:not(:last-child) {
    border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
  }

  &:focus-within {
    background: rgba(24, 144, 255, 0.04);
    border-bottom-color: #1890ff;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 2rpx;
      background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
      animation: slideIn 0.3s ease;
    }
  }
}

@keyframes slideIn {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* 输入框样式优化 */
.input-with-unit {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  padding: 8rpx 0;
}

.unit-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #1890ff;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  min-width: 80rpx;
  text-align: center;
  border: 1rpx solid rgba(24, 144, 255, 0.2);
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

/* 扫描输入框样式 */
.input-with-scan {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  padding: 8rpx 0;
}

.scan-button {
  display: flex;
  align-items: center;
  gap: 10rpx;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  border: 2rpx solid #1890ff;
  border-radius: 16rpx;
  padding: 12rpx 18rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
  }

  &:active {
    background: linear-gradient(135deg, #bae7ff 0%, #e6f7ff 100%);
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.2);

    &::before {
      left: 100%;
    }
  }
}

/* 位置信息特殊样式 */
.location-field {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
  border-left: 4rpx solid #52c41a;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(82, 196, 26, 0.05) 0%, rgba(24, 144, 255, 0.05) 100%);
    pointer-events: none;
  }
}

.location-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 8rpx 0;
}

.location-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border: 2rpx solid #52c41a;
  border-radius: 12rpx;
  padding: 12rpx 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.2);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  min-width: 120rpx;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  &:active {
    background: linear-gradient(135deg, #389e0d 0%, #52c41a 100%);
    transform: scale(0.95);
    box-shadow: 0 1rpx 4rpx rgba(82, 196, 26, 0.3);

    &::before {
      left: 100%;
    }
  }
}

.btn-text {
  font-size: 22rpx;
  font-weight: 600;
  color: #fff;
  letter-spacing: 0.3rpx;
  white-space: nowrap;
}

.address-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
  border: 2rpx solid #1890ff;
  border-radius: 12rpx;
  padding: 12rpx 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.15);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  min-width: 120rpx;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
  }

  &:active {
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
    transform: scale(0.95);
    box-shadow: 0 1rpx 4rpx rgba(24, 144, 255, 0.2);

    &::before {
      left: 100%;
    }
  }
}

.address-text {
  font-size: 22rpx;
  font-weight: 600;
  color: #1890ff;
  letter-spacing: 0.3rpx;
  white-space: nowrap;
}

/* 坐标输入组 */
.coordinate-group {
  display: flex;
  gap: 1rpx;
  background: rgba(240, 240, 240, 0.6);
  border-radius: 12rpx;
  overflow: hidden;
  margin: 8rpx 0;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.coordinate-item {
  flex: 1;
  background: #fff;
  transition: all 0.3s ease;

  &:first-child {
    border-radius: 12rpx 0 0 12rpx;
  }

  &:last-child {
    border-radius: 0 12rpx 12rpx 0;
  }

  &:focus-within {
    background: rgba(24, 144, 255, 0.02);
    box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.2);
    z-index: 1;
  }
}

/* 照片上传区域 */
.photo-field {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2e8 100%);
  border-left: 4rpx solid #fa8c16;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(250, 140, 22, 0.05) 0%, rgba(255, 169, 64, 0.05) 100%);
    pointer-events: none;
  }
}

.photo-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  flex: 1;
  padding: 16rpx 0;
}

.upload-tip {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 24rpx;
  color: #8c8c8c;
  background: rgba(140, 140, 140, 0.1);
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  border: 1rpx dashed #d9d9d9;
  transition: all 0.3s ease;
}

.tip-text {
  font-size: 24rpx;
  color: #8c8c8c;
  font-weight: 500;
}

/* 提交按钮区域 */
.submit-section {
  padding: 40rpx 32rpx;
  background: linear-gradient(135deg, #fff 0%, #f8faff 100%);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  margin-top: 32rpx;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60rpx;
    height: 4rpx;
    background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
    border-radius: 2rpx;
  }
}

/* OCR弹窗样式 */
.ocr-popup {
  background: linear-gradient(135deg, #fff 0%, #f8faff 100%);
  border-radius: 24rpx;
  overflow: hidden;
  margin: 0 auto;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.popup-header {
  padding: 32rpx;
  background: linear-gradient(135deg, #f8faff 0%, #f0f8ff 100%);
  border-bottom: 1rpx solid rgba(240, 240, 240, 0.6);
  text-align: center;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80rpx;
    height: 2rpx;
    background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
    border-radius: 1rpx;
  }
}

.popup-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

.popup-subtitle {
  font-size: 24rpx;
  color: #666;
  font-weight: 400;
  opacity: 0.8;
}

.popup-content {
  padding: 32rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.text-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.text-item {
  background: linear-gradient(135deg, #f8faff 0%, #f0f8ff 100%);
  border: 2rpx solid rgba(24, 144, 255, 0.2);
  border-radius: 12rpx;
  padding: 12rpx;
  margin: 6rpx 0;
  text-align: center;
  font-size: 18rpx;
  color: #1a1a1a;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(24, 144, 255, 0.1), transparent);
    transition: left 0.5s ease;
  }

  &:active {
    transform: scale(0.95);

    &::before {
      left: 100%;
    }
  }

  &.text-item-selected {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: #fff;
    border-color: #1890ff;
    box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);
    transform: scale(1.05);

    &::before {
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    }
  }
}

.popup-footer {
  padding: 24rpx 32rpx 32rpx;
  background: linear-gradient(135deg, #f8faff 0%, #f0f8ff 100%);
  border-top: 1rpx solid rgba(240, 240, 240, 0.6);
}

/* Canvas样式 */
.canvas {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  width: 1rpx;
  height: 1rpx;
  opacity: 0;
  pointer-events: none;
}

/* 全局组件样式覆盖 */
:deep(.modern-input) {
  border-radius: 12rpx !important;
  border: 1rpx solid rgba(240, 240, 240, 0.8) !important;
  background: rgba(248, 250, 255, 0.5) !important;
  transition: all 0.3s ease !important;

  &:focus {
    border-color: #1890ff !important;
    background: rgba(24, 144, 255, 0.05) !important;
    box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.1) !important;
  }
}

:deep(.modern-picker) {
  border-radius: 12rpx !important;
  border: 1rpx solid rgba(240, 240, 240, 0.8) !important;
  background: rgba(248, 250, 255, 0.5) !important;
  transition: all 0.3s ease !important;

  &:focus {
    border-color: #1890ff !important;
    background: rgba(24, 144, 255, 0.05) !important;
    box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.1) !important;
  }
}

:deep(.modern-textarea) {
  border-radius: 12rpx !important;
  border: 1rpx solid rgba(240, 240, 240, 0.8) !important;
  background: rgba(248, 250, 255, 0.5) !important;
  transition: all 0.3s ease !important;

  &:focus {
    border-color: #1890ff !important;
    background: rgba(24, 144, 255, 0.05) !important;
    box-shadow: 0 0 0 2rpx rgba(24, 144, 255, 0.1) !important;
  }
}

:deep(.radio-group-modern) {
  gap: 12rpx !important;
}

:deep(.radio-modern) {
  border-radius: 12rpx !important;
  border: 2rpx solid rgba(24, 144, 255, 0.2) !important;
  background: rgba(248, 250, 255, 0.5) !important;
  transition: all 0.3s ease !important;

  &.is-checked {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
    border-color: #1890ff !important;
    color: #fff !important;
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3) !important;
  }
}

:deep(.submit-button) {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  border: none !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3) !important;
  font-weight: 600 !important;
  font-size: 32rpx !important;
  padding: 24rpx !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  &:active {
    transform: scale(0.98) !important;
    box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.4) !important;
  }
}

:deep(.confirm-button) {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
  border: none !important;
  border-radius: 16rpx !important;
  box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.3) !important;
  font-weight: 600 !important;
  font-size: 28rpx !important;
  transition: all 0.3s ease !important;

  &:active {
    transform: scale(0.98) !important;
    box-shadow: 0 3rpx 12rpx rgba(82, 196, 26, 0.4) !important;
  }
}

:deep(.form-group) {
  border-radius: 0 !important;
  border: none !important;
  background: transparent !important;
}
</style>
