<template>
  <div class="field-work">
    <template v-for="item in list" :key="item.id">
      <wd-card @click="handleClick(item)" class="box-shadow">
        <div class="f-y-center">
          <wd-img class="mar-R20" :width="30" :height="30" :src="handlerImage(item.point_Type)" />
          <text>{{ item.address }}</text>
        </div>
        <template #footer>{{ item.created_Time.slice(0, 16).replace('T', ' ') }}</template>
        <template #title>{{ item.zone_Name }}</template>
      </wd-card>
    </template>
    <div class="back-white border-R14 box-shadow">
      <wd-status-tip image="search" v-if="!list.length" tip="无数据" />
    </div>
  </div>
  <div style="padding: 80rpx"></div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { getalveVerificationListApi } from '/src/services/model/submit.js'
import uniUtil from '/src/utils/uniUtil.js'
import { userHomeStore } from '/src/store/home.js'
const { deliveryDots } = userHomeStore()

const props = defineProps({ code: String })

watch(() => props.code, getList, { immediate: true })

const list = ref([])
watch(list, (val) => deliveryDots(val, 'field'))

async function getList(code) {
  if (!code) return
  try {
    const { data } = await getalveVerificationListApi(code)
    list.value = data.valveVerification
  } catch (error) {
    list.value = []
  }
}
function handlerImage(type) {
  const url = type === '总表' ? 'https://www.szwgft.cn:8090/AppIcon/50cea3d9-96c7-43ce-8b70-619d8996964a.png' : 'https://www.szwgft.cn:8090/AppIcon/58b25c06-034c-4066-87d4-0e20135ee5fc.png'
  return url
}
function handleClick(val) {
  uniUtil.navigateTo(`/src/pages/mine/children/field-work/detail?id=${val.id}`)
}
</script>

<style lang="less" scoped>
.field-work {
}
</style>
