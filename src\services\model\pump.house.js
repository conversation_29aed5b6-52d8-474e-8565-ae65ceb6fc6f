import { nodeRequest } from '../index'

class PumpHouse {
  list() {
    return nodeRequest.get('/nodeServer/pumpHouse/list?all=true')
  }
  create(data) {
    return nodeRequest.post('/nodeServer/pumpHouse', data)
  }
  update(data) {
    return nodeRequest.put('/nodeServer/pumpHouse', data)
  }
  detail(pumpRoomNumber) {
    return nodeRequest.get(`/nodeServer/pumpHouse/detail/${pumpRoomNumber}`)
  }
  createNode(data) {
    return nodeRequest.post('/nodeServer/pumpHouse/node', data)
  }
  updateNode(data) {
    return nodeRequest.put('/nodeServer/pumpHouse/node', data)
  }

  nodeList(pumpRoomNumber) {
    return nodeRequest.get(`/nodeServer/pumpHouse/node/list/${pumpRoomNumber}`)
  }
  nodeDetail(pumpRoomNumber, nodeNum) {
    return nodeRequest.get(`/nodeServer/pumpHouse/node/${pumpRoomNumber}?Node=${nodeNum}`)
  }
  seek(pumpHouseName) {
    return nodeRequest.get(`/nodeServer/pumpHouse/seek?pumpHouseName=${pumpHouseName}`)
  }
  seekScope(ZoneCode) {
    return nodeRequest.get(`/nodeServer/pumpHouse/seekScope?ZoneCode=${ZoneCode}`)
  }
  createNodeFile(data) {
    return nodeRequest.post('/nodeServer/pumpHouse/nodeFile', data)
  }
}

export const PumpHouseApi = new PumpHouse()
