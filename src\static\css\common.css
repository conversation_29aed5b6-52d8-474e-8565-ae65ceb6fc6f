.height {
  height: 100%;
}

.width {
  width: 100%;
}

.flex {
  display: flex;
}

.juco {
  justify-content: center;
}

.juspb {
  justify-content: space-between;
}

.alit {
  align-items: center;
}

.direction {
  flex-direction: column;
}

.direction-r {
  flex-direction: row-reverse;
}

.flex-xy-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-xy-b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex1 {
  flex: 1;
}

.fn-s20 {
  font-size: 20rpx;
}

.fn-s20 {
  font-size: 20rpx;
}

.fn-s20 {
  font-size: 20rpx;
}

.fn-s22 {
  font-size: 22rpx;
}

.fn-s24 {
  font-size: 24rpx;
}

.fn-s26 {
  font-size: 26rpx;
}

.fn-s28 {
  font-size: 28rpx;
}

.fn-s30 {
  font-size: 30rpx;
}

.fn-s32 {
  font-size: 32rpx;
}

.fn-s34 {
  font-size: 34rpx;
}

.fn-s36 {
  font-size: 36rpx;
}

.fn-s38 {
  font-size: 38rpx;
}

.fn-w500 {
  font-weight: 500;
}

.fn-w600 {
  font-weight: 600;
}

.fn-w700 {
  font-weight: 700;
}

.mr-l24 {
  margin-left: 24rpx;
}

.mr-t24 {
  margin-top: 24rpx;
}

.mr-r24 {
  margin-right: 24rpx;
}

.mr-b24 {
  margin-bottom: 24rpx;
}

.mr-x24 {
  margin: 0 24rpx;
}

.mr-y24 {
  margin: 24rpx 0;
}

.mr-l12 {
  margin-left: 12rpx;
}

.mr-t12 {
  margin-top: 12rpx;
}

.mr-r12 {
  margin-right: 12rpx;
}

.mr-b12 {
  margin-bottom: 12rpx;
}

.mr-x12 {
  margin: 0 12rpx;
}

.mr-y12 {
  margin: 12rpx 0;
}

.pa-24 {
  padding: 24rpx;
}

.pa-l24 {
  padding-left: 24rpx;
}

.pa-t24 {
  padding-top: 24rpx;
}

.pa-r24 {
  padding-right: 24rpx;
}

.pa-b24 {
  padding-bottom: 24rpx;
}

.pa-x24 {
  padding: 0 24rpx;
}

.pa-y24 {
  padding: 24rpx 0;
}

.pa-x12 {
  padding: 0 12rpx;
}

.pa-y12 {
  padding: 12rpx 0;
}

.pa-l12 {
  padding-left: 12rpx;
}

.pa-t12 {
  padding-top: 12rpx;
}

.pa-r12 {
  padding-right: 12rpx;
}

.pa-b12 {
  padding-bottom: 12rpx;
}

.c999 {
  color: #999;
}

.c666 {
  color: #666;
}

.cfff {
  color: #fff;
}

.bgfff {
  background-color: #fff;
}

.bg {
  background-color: aquamarine;
}

.mapboxgl-ctrl {
  display: none !important;
}
