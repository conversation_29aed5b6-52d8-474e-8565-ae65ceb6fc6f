import { nodeRequest } from '../index'

class VerifyPipe {
  get(id) {
    return nodeRequest.get(`/nodeServer/verify?SystemId=${id}`)
  }
  update(data) {
    return nodeRequest.put(`/nodeServer/verify`, data)
  }
  distinguish(data) {
    return nodeRequest.post(`/nodeServer/verify`, data)
  }

  search(data) {
    return nodeRequest.post(`/nodeServer/verify/search`, data)
  }

  list() {
    return nodeRequest.get(`/nodeServer/verify/list`)
  }
  statistics() {
    return nodeRequest.get(`/nodeServer/verify/statistics`)
  }
}

export const VerifyPipeApi = new VerifyPipe()
