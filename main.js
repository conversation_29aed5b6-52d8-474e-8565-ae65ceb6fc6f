import App from './App'
import 'mapbox-gl/dist/mapbox-gl.css'
import './src/static/css/common.css'
import './src/static/css/index.css'
import { createPinia } from 'pinia'
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  app.use(createPinia())
  return {
    app
  }
}
// #endif
