/**
 * 工具类模块，封装了uni-app的一些常用页面跳转和本地存储方法。
 */
class Utils {
  
  /**
   * 页面到应用内的某个页面,跳转到应用内的某个页面。
   * @param {string} url - 需要跳转的页面路径。
   */
  navigateTo(url){
    uni.navigateTo({url})
  }
  
  /**
   * 页面重定向到应用内的某个页面。关闭当前页面，跳转到应用内的某个页面。
   * @param {string} url - 需要跳转的页面路径。
   */
  redirectTo(url) {
    uni.redirectTo({ url });
  }

  /**
   * 关闭所有页面，打开到应用内的某个页面。
   * @param {string} url - 需要跳转的页面路径。
   */
  reLaunch(url) {
    uni.reLaunch({ url });
  }

  /**
   * 切换到 tabBar 页面，并关闭其他所有非 tabBar 页面。
   * @param {string} url - tabBar 页面的路径。
   */
  switchTab(url) {
    uni.switchTab({ url });
  }

  /**
   * 关闭当前页面，返回上一页面或多级页面。
   * @param {number} [delta=1] - 返回的页面数，如果 delta 大于现有页面数，则返回到首页。
   */
  navigateBack(delta = 1) {
    uni.navigateBack({ delta });
  }

  /**
   * 获取本地存储中的指定 key 对应的内容。
   * @param {string} key - 本地存储中的键名。
   * @returns {any} - 存储的内容，如果没有则返回 undefined。
   */
  get(key) {
    const result = uni.getStorageSync(key);
    return result ?? void[0]
  }

  /**
   * 将数据存入本地存储中。
   * @param {string} key - 本地存储的键名。
   * @param {any} data - 需要存储的数据。
   */
  set(key, data) {
    uni.setStorageSync(key, data);
  }

  /**
   * 删除本地存储中指定 key 的数据。
   * @param {string} key - 需要删除的键名。
   */
  remove(key) {
    uni.removeStorageSync(key);
  }

  /**
   * 清理本地存储中的所有数据。
   */
  clear() {
    uni.clearStorageSync();
  }
  
  showToast(title){
    uni.showToast({title,icon:'none'})
  }
}

// 导出工具类实例，使其可以在其他模块中被使用。
export default new Utils();