import { createApp } from 'vue'

/**
 * 创建一个过渡组件。
 *
 * @param {Component} component - 要创建的组件。
 * @returns {Function} 返回一个函数，这个函数接收props作为参数，然后返回一个Mounted的DOM元素。
 */
export const transitionComponent = (component) => (props) => {
  // 创建一个div元素作为容器
  const dom = document.createElement('div')
  // 使用传入的组件和属性创建一个应用实例，并挂载到dom上
  const { $el } = createApp(component, props).mount(dom)
  // 返回挂载后的元素
  return $el
}
