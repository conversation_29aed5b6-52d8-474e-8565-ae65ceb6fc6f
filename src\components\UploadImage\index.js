import { imageBaseUrl, ServerBaseUrl } from '/src/config/index.json'
import uniUtil from '../../utils/uniUtil'

/**
 * 图片预览
 * @param {*} urls 图片地址数组
 * @param {*} current 当前预览图片
 */
export function previewImage(urls, current) {
  uni.previewImage({
    urls: urls.map((url) => handlerImage(url)), // 需要预览的图片链接列表
    current: handlerImage(current) // 当前显示图片的链接
  })
}

/**
 * 图片选择
 * @returns {Promise}
 */
export function chooseImage() {
  return new Promise((resovle, reject) => {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => resovle(res),
      fail: (err) => reject(err)
    })
  })
}

/**
 * 获取图片信息
 * @param {*} url 图片地址
 * @returns {Promise}
 */
export function getImageInfo(url) {
  return new Promise((resovle, reject) => {
    uni.getImageInfo({
      src: url,
      success: (res) => resovle(res),
      fail: (err) => reject(err)
    })
  })
}

/**
 * 图片压缩
 * @param {*} width  宽度
 * @param {*} height   高度
 * @param {*} canvasId  画布Id
 * @param {*} quality 压缩质量
 */
export function canvasToTempFilePath(width, height, canvasId, quality) {
  return new Promise((resovle, reject) => {
    setTimeout(() => {
      uni.canvasToTempFilePath({
        width,
        height,
        canvasId,
        quality,
        fileType: 'jpg',
        success: (res) => resovle(res),
        fail: (err) => reject(err)
      })
    }, 200)
  })
}

/**
 * 图片上传
 * @param {*} path 临时图片地址
 * @param {*} code 所需上传到的文件名
 * @returns
 */
export function uploadFile(path, code) {
  return new Promise((resovle, reject) => {
    const header = {}
    const userInfo = uniUtil.get('userInfo')

    header.Authorization = 'Bearer ' + userInfo.token ?? ''

    uni.uploadFile({
      url: `${ServerBaseUrl}/api/UpWrite/upload?Zone_Code=${code}`,
      filePath: path,
      header,
      name: 'file',
      success: (res) => {
        if (res.statusCode === 401) {
          uniUtil.navigateTo('/src/pages/login/index?type=1')
        }
        resovle(JSON.parse(res.data))
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 图片拼接地址
export function handlerImage(url) {
  const path = imageBaseUrl + url
  return path
}
