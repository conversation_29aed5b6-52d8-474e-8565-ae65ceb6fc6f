<template>
  <view class="map-search-container">
    <!-- 现代化搜索框 -->
    <view class="search-wrapper">
      <wd-search v-model="text" @focus="emit('focus')" @search="handleSearch" placeholder="搜索小区名称或编码" custom-class="modern-search" :clearable="true" :show-action="false" />
      <view class="search-decoration">
        <view class="decoration-circle circle-1"></view>
        <view class="decoration-circle circle-2"></view>
      </view>
    </view>

    <!-- 现代化搜索结果弹窗 -->
    <wd-popup v-model="show" position="bottom" :modal="true" custom-style="border-radius: 32rpx 32rpx 0 0; overflow: hidden;" :close-on-click-modal="true">
      <view class="search-results">
        <!-- 弹窗头部 -->
        <view class="results-header">
          <view class="header-indicator"></view>
          <view class="header-title">
            <wd-icon name="location" size="32rpx" color="#1890ff" />
            <text class="title-text">搜索结果</text>
            <text class="title-count">({{ actions.length }})</text>
          </view>
        </view>

        <!-- 搜索结果列表 -->
        <view class="results-content">
          <view v-if="loading" class="loading-state">
            <wd-loading type="circular" size="48rpx" />
            <text class="loading-text">搜索中...</text>
          </view>

          <view v-else-if="actions.length === 0" class="empty-state">
            <wd-icon name="search" size="96rpx" color="#d9d9d9" />
            <text class="empty-text">未找到相关小区</text>
            <text class="empty-subtitle">请尝试其他关键词</text>
          </view>

          <view v-else class="results-list">
            <view v-for="(item, index) in actions" :key="index" class="result-item" @click="handleSelect(item)">
              <view class="item-icon">
                <wd-icon name="home" size="32rpx" color="#52c41a" />
              </view>
              <view class="item-content">
                <view class="item-name">{{ item.name }}</view>
                <view class="item-code">编码: {{ item.Zone_Code }}</view>
              </view>
              <view class="item-arrow">
                <wd-icon name="arrow-right" size="24rpx" color="#999" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { getZoneDataLike } from '/src/services/model/map.js'
import uniUtil from '/src/utils/uniUtil.js'

const emit = defineEmits(['select', 'focus'])
const show = ref(false)
const loading = ref(false)
const actions = ref([])
const text = ref('')

async function handleSearch({ value }) {
  try {
    if (!value) {
      uniUtil.showToast('请输入搜索关键词')
      return
    }

    loading.value = true
    show.value = true
    actions.value = []

    const res = await getZoneDataLike(value)
    const result = JSON.parse(res.data)

    actions.value = result.map((i) => ({
      name: i.Zone_Name,
      Zone_Code: i.Zone_Code,
      Center_Point: i.Center_Point.split(',')
    }))

    if (!result.length) {
      // 空状态会通过模板显示，不需要toast
    }
  } catch (error) {
    uniUtil.showToast('搜索失败，请重试')
    console.error('搜索错误:', error)
  } finally {
    loading.value = false
  }
}

function handleSelect(item) {
  show.value = false
  emit('select', item)
}
</script>

<style lang="scss" scoped>
.map-search-container {
  position: relative;
  width: 100%;
}

/* 搜索框包装器 */
.search-wrapper {
  position: relative;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8faff 0%, #e8f4ff 100%);
  border-radius: 0 0 32rpx 32rpx;
  overflow: hidden;
}

/* 搜索装饰元素 */
.search-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(82, 196, 26, 0.1) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: -60rpx;
  right: 60rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  bottom: -40rpx;
  left: 40rpx;
  animation-delay: 3s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
    opacity: 0.8;
  }
}

/* 现代化搜索框样式 */
:deep(.modern-search) {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(24, 144, 255, 0.15), 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
}

:deep(.modern-search:focus-within) {
  box-shadow: 0 12rpx 48rpx rgba(24, 144, 255, 0.25), 0 6rpx 24rpx rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
  transform: translateY(-2rpx);
}

/* 搜索结果弹窗 */
.search-results {
  background: #ffffff;
  min-height: 600rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

/* 弹窗头部 */
.results-header {
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: linear-gradient(135deg, #fafbff 0%, #f0f8ff 100%);
}

.header-indicator {
  width: 80rpx;
  height: 8rpx;
  background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
  border-radius: 4rpx;
  margin: 0 auto 24rpx;
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.title-count {
  font-size: 24rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 搜索结果内容区域 */
.results-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 32rpx 32rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  gap: 24rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  gap: 16rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.empty-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 搜索结果列表 */
.results-list {
  padding-top: 24rpx;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
  cursor: pointer;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:active {
  background: rgba(24, 144, 255, 0.05);
  transform: scale(0.98);
}

.item-icon {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.item-code {
  font-size: 24rpx;
  color: #666;
  line-height: 1.3;
}

.item-arrow {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(153, 153, 153, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.result-item:active .item-arrow {
  background: rgba(24, 144, 255, 0.2);
  transform: translateX(4rpx);
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .search-wrapper {
    padding: 20rpx;
  }

  .results-header {
    padding: 24rpx 24rpx 20rpx;
  }

  .results-content {
    padding: 0 24rpx 24rpx;
  }

  .item-icon {
    width: 56rpx;
    height: 56rpx;
    margin-right: 20rpx;
  }

  .item-name {
    font-size: 28rpx;
  }

  .item-code {
    font-size: 22rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .search-wrapper {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  }

  .search-results {
    background: #1a1a1a;
  }

  .results-header {
    background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
    border-bottom-color: #333;
  }

  .title-text {
    color: #fff;
  }

  .result-item {
    border-bottom-color: #333;
  }

  .item-name {
    color: #fff;
  }

  .item-code {
    color: #ccc;
  }

  .empty-text {
    color: #ccc;
  }

  .empty-subtitle {
    color: #999;
  }

  .loading-text {
    color: #ccc;
  }
}
</style>
