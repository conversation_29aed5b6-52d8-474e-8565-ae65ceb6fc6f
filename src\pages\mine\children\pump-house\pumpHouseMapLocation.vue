<template>
  <div class="pump-house-map all">
    <wd-search custom-class=" absolute z-index-99 W100 border-B-eee" v-model="seekText" @search="search" @cancel="() => (sheetShow = false)" />
    <MapBox :variation @pumpHouseClick="handlePumpHouseClick" />
    <div class="absolute box-shadow f-xy-center pad-10 back-white border-R12 z-index-99" @click="handleOneself" style="bottom: 180rpx; right: 50rpx"><image style="width: 50rpx; height: 50rpx" src="/static/DW.png"></image></div>
    <div class="legend box-shadow absolute z-index-99 back-white">
      <div class="f-y-center" style="margin: 4rpx 0">
        <image class="mar-R10" style="width: 30rpx; height: 30rpx" :src="`https://www.szwgft.cn/nodeServer/img/4.png`"></image>
        <div class="fon-S24 mar-R16">已采集</div>
        <div class="fon-S24">{{ legend['已采集'] }}</div>
      </div>
      <div class="f-y-center" style="margin: 4rpx 0">
        <image class="mar-R10" style="width: 30rpx; height: 30rpx" :src="`https://www.szwgft.cn/nodeServer/img/2.png`"></image>
        <div class="fon-S24 mar-R16">未采集</div>
        <div class="fon-S24">{{ legend['未采集'] }}</div>
      </div>
    </div>
    <wd-action-sheet v-model="sheetShow" :actions="actions" @select="seekSelectClick" />
  </div>

  <wd-floating-panel custom-class="floating-panel_c relative" v-model:height="height" :anchors="anchors">
    <div class="pad-X24" v-if="detail">
      <wd-icon custom-class=" absolute close_icon" @click="height = 0" name="close" size="18px"></wd-icon>
      <div class="back-white box-shadow mar-B24 pad-20 border-R10 text-center fon-S28 fon-W600">{{ detail?.PumpHouseName }}</div>

      <wd-cell-group custom-class="mar-B22  relative border-R12 overflow-hidden" title="位置信息" border>
        <wd-cell title="坐标" :value="detail.X + ',' + detail.Y" />
        <wd-cell title="所属街道" :value="detail.BelongingStreet" />
        <wd-cell title="所属片区" prop="belongingArea" :value="detail.BelongingArea" />
        <wd-cell title="所属网格" :value="detail.Gridding" />
        <wd-textarea label="泵房精确位置" auto-height type="textarea" v-model="detail.AccuratePosition" :maxlength="200" show-word-limit placeholder="泵房精确位置填写规则 如：xxx小区xxx栋地下二层" clearable prop="AccuratePosition" />
        <div class="updateIcon absolute" @click="open = true">
          <wd-icon name="location" size="16px" color="#fff"></wd-icon>
        </div>
        <div style="border-top: 2rpx solid #ebedf0" class="pad-24">
          <div class="">泵房图片</div>
          <div>
            <UploadImage :maxlength="3" :url v-model="detail.PumpHouseImg" />
          </div>
        </div>
      </wd-cell-group>
    </div>
    <div class="flex f-between" style="margin: 30px 50px">
      <wd-button type="success" @click="openNavigation" icon="location">导航</wd-button>
      <wd-button icon="edit-1" @click="openUpdate">保存修改</wd-button>
    </div>
    <div style="height: 20px"></div>
    <CoordinatePickup v-if="detail && open" v-model="open" :MarkerCenter @change="change" />
  </wd-floating-panel>

  <div class="wrap f-xy-center" v-if="show">
    <div class="content pad-14 f-y-center f-between f-column">
      <div class="text-center mar-10 fon-W600">上传中</div>
      <wd-loading />
      <wd-progress :percentage hide-text />
    </div>
  </div>
  <wd-toast />
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { PumpHouseApi } from '/src/services/model/pump.house.js'
import { CommonApi } from '/src/services/model/common.js'
import { mapNavigation } from '/src/utils'
import MapBox from './components/MapBox/index.vue'
import CoordinatePickup from './components/CoordinatePickup/index.vue'
import UploadImage from './components/UploadImage/index.vue'

import { useToast } from 'wot-design-uni'

const toast = useToast()

onShow(async () => {
  getPumpHouseList({ all: true })
  if (detail.value) {
    const { data } = await PumpHouseApi.detail(detail.value.PumpRoomNumber)
    detail.value = data
  }
})
const variation = ref(null)

const height = ref(0)
const anchors = ref([])
const seekText = ref('')
const sheetShow = ref(false)
const open = ref(false)
const MarkerCenter = ref(null)

const pumpHouseNode = ref([])
const url = computed(() => `https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${detail.value.PumpRoomNumber}/img`)

onLoad(async () => {
  const windowHeight = uni.getSystemInfoSync().windowHeight
  anchors.value = [0, Math.round(1 * windowHeight)]
  height.value = anchors.value[0]
  pumpHouseNode.value = await queryDictionaries('pumpHouseNode')
})

const pumpHouseDots = ref([])
const legend = ref({ 已采集: 0, 未采集: 0 })
async function getPumpHouseList() {
  try {
    const { data } = await PumpHouseApi.list()
    pumpHouseDots.value = data.map((item) => {
      const state = item.ProgressStatus != '正常' || item.CurrentNode < 4 ? '异常' : item.CurrentNode < 12 ? '临供' : item.CurrentNode < 14 ? '切换' : '初验'
      const isCollect = item.AccuratePosition ? '已采集' : '未采集'
      legend.value[isCollect] += 1
      item.Id = JSON.stringify(item)
      return {
        type: 'Feature',
        geometry: { type: 'Point', coordinates: [item.X, item.Y] },
        properties: { ...item, state, isCollect }
      }
    })
    variation.value = { type: 'updatePumpHouseDots', value: pumpHouseDots.value }
  } catch (error) {}
}

const detail = ref(null)
const CurrentNode = ref(0)
function handlePumpHouseClick(item) {
  detail.value = JSON.parse(item.Id)
  MarkerCenter.value = [item.X, item.Y]
  CurrentNode.value = detail.value.CurrentNode - 1
  height.value = anchors.value[1]
}

const pumpHouseNodeDetail = ref(null)
const pumpHouseNodeFileKeyMap = new Map()
watch(CurrentNode, async (val) => {
  if (!pumpHouseNodeFileKeyMap.has(val + 1)) {
    const { data: FileKey } = await CommonApi.queryDictionaries(`pumpHouseNodeFile_${val + 1}`)
    const data = FileKey.map(({ DictCode, DictValue }) => [DictCode, DictValue])
    pumpHouseNodeFileKeyMap.set(val + 1, Object.fromEntries(data))
  }
  const { data } = await PumpHouseApi.nodeDetail(detail.value.PumpRoomNumber, val + 1)
  if (data?.Files) {
    const nodeFiles = JSON.parse(data.Files)
    const pumpHouseNodeFileKey = pumpHouseNodeFileKeyMap.get(val + 1)
    data.Files = nodeFiles.map((file) => {
      file.FileName = pumpHouseNodeFileKey[file.FileType]
      return file
    })
  }

  pumpHouseNodeDetail.value = data
})
watch(height, (val) => {
  if (val === 0) {
    detail.value = null
  }
})

// 获取节点列表

async function queryDictionaries(type) {
  try {
    const { data } = await CommonApi.queryDictionaries(type)
    return data
  } catch (error) {
    message.error(error.message)
  }
}

async function openNavigation() {
  const { data } = await CommonApi.coordinateTransformation({ lat: detail.value.Y, lng: detail.value.X })
  const lat = Number(detail.value.Y) + Number(data.lat.replace(detail.value.Y, ''))
  const lng = Number(detail.value.X) + Number(data.lng.replace(detail.value.X, ''))
  mapNavigation({ lat, lng, name: detail.value.PumpHouseName })
}

async function openUpdate() {
  try {
    await PumpHouseApi.update(detail.value)
    toast.success('泵房信息更新成功')
    getPumpHouseList({ all: true })
    setTimeout(() => (height.value = 0), 1500)
  } catch (error) {
    toast.error('泵房信息更新失败')
  }
}

function previewImage(url) {
  uni.previewImage({
    urls: detail.value.PumpHouseImg.split(','), // 需要预览的图片链接列表
    current: url // 当前显示图片的链接
  })
}

const actions = ref([])
async function search() {
  try {
    const { data } = await PumpHouseApi.seek(seekText.value)
    actions.value = data.map((item) => ({ ...item, name: item.PumpHouseName }))
    sheetShow.value = true
  } catch (error) {}
}

function seekSelectClick(e) {
  const { X, Y } = e.item
  variation.value = { type: 'moveto', value: [X, Y] }
  seekText.value = ''
}

async function handleOneself() {
  const { latitude, longitude } = await uni.getLocation({ altitude: true })
  variation.value = { type: 'moveto', value: [longitude, latitude] }
}

function change(e) {
  detail.value.X = e.x.toString()
  detail.value.Y = e.y.toString()
  detail.value.Gridding = e.feature.Grid.replace('FD', '福东').replace('FZ', '福中').replace('ML', '梅林').replace('XM', '香蜜')
  detail.value.BelongingStreet = e.feature.Subdistric
  detail.value.BelongingArea = e.feature.Belong_Are
  open.value = false
}
</script>

<style lang="less" scoped>
.pump-house-map {
}

.close_icon {
  top: 0;
  right: 16px;
  z-index: 99;
}

.floating-panel_c {
  background-color: rgba(255, 255, 255, 0.7);
  :deep(.wd-floating-panel__header) {
    height: 14px;
  }
  :deep(.wd-floating-panel__content) {
    background-color: transparent;
  }
}
:deep(.wd-tabs__map-nav-btn) {
  width: auto;
  height: auto;
  padding: 8rpx 18rpx;
  margin-bottom: 10rpx;
  line-height: normal;
}
.legend {
  bottom: 44rpx;
  left: 24rpx;
  padding: 16rpx;
  border-radius: 12rpx;
}

.updateIcon {
  top: 16rpx;
  right: 26rpx;
  padding: 4rpx 18rpx;
  background: #15a131;
  border-radius: 12rpx;
}
</style>
